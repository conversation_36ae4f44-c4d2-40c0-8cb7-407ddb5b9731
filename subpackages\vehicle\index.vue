<template>
  <view class="vehicle-container">
    <!-- 搜索区域 -->
    <uni-section title="搜索条件" type="line">
      <view class="search-box">
        <view class="search-item">
          <input v-model="queryParams.licensePlate" placeholder="请输入车牌号" class="search-input" />
        </view>
        <view class="search-item">
          <input v-model="queryParams.userNickName" placeholder="请输入车主姓名" class="search-input" />
        </view>
        <button @click="handleQuery" class="cu-btn bg-blue">搜索</button>
        <button @click="resetQuery" class="cu-btn line-blue">重置</button>
        <button v-if="isAdmin" @click="handleAdd" class="cu-btn bg-green">新增</button>
      </view>
    </uni-section>

    <!-- 列表区域 -->
    <uni-section title="车辆列表" type="line">
      <view class="list-box">
        <view v-if="vehicleList.length === 0" class="empty-box">
          暂无数据
        </view>
        <template v-else>
          <view v-for="(item, index) in vehicleList" :key="index" class="list-item">
            <view class="item-main">
              <view class="item-title">
                <text class="plate-number">{{item.licensePlate}}</text>
                <text class="vehicle-brand">{{item.vehicleBrand}} {{item.vehicleModel}}</text>
              </view>
              <view class="item-info">
                <text>所属部门：{{(item.user && item.user.dept && item.user.deptName) || '-'}}</text>
                <text>车主姓名：{{(item.user && item.user.nickName) || '-'}}</text>
                <text>联系方式：{{(item.user && item.user.phonenumber) || '-'}}</text>
                <text>备注：{{item.remark || '-'}}</text>
              </view>
            </view>
            <view class="item-actions" v-if="isAdmin">
              <button @click="handleUpdate(item)" class="cu-btn bg-blue sm">编辑</button>
              <button @click="handleDelete(item)" class="cu-btn bg-red sm">删除</button>
            </view>
          </view>
          <view class="pagination-box" v-if="total > 0">
            <uni-pagination
              :total="total"
              :pageSize="queryParams.pageSize"
              :current="queryParams.pageNum"
              @change="handlePageChange"
            />
          </view>
        </template>
      </view>
    </uni-section>

    <!-- 弹窗 -->
    <view v-if="showPopup" class="popup-mask" @click="dialogClose">
      <view class="popup-wrapper" @click.stop>
        <view class="popup-content">
          <view class="popup-title">{{formTitle}}</view>
          <view class="form">
            <view class="form-item">
              <text class="label">车牌号码</text>
              <input v-model="form.licensePlate" placeholder="请输入车牌号" class="input" />
            </view>
            <view class="form-item">
              <text class="label">车辆品牌</text>
              <input v-model="form.vehicleBrand" placeholder="请输入车辆品牌" class="input" />
            </view>
            <view class="form-item">
              <text class="label">车辆型号</text>
              <input v-model="form.vehicleModel" placeholder="请输入车辆型号" class="input" />
            </view>
            <view class="form-item">
              <text class="label">车辆颜色</text>
              <input v-model="form.vehicleColor" placeholder="请输入车辆颜色" class="input" />
            </view>
            <view class="form-item">
              <text class="label">备注说明</text>
              <input v-model="form.remark" placeholder="请输入备注说明" class="input" />
            </view>
          </view>
          <view class="popup-footer">
            <button @click="dialogClose" class="cu-btn line-blue">取消</button>
            <button @click="dialogConfirm" class="cu-btn bg-blue">确定</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import uniSection from '@/components/uni-section/uni-section.vue';
import uniPagination from '@/uni_modules/uni-pagination/components/uni-pagination/uni-pagination.vue';
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue';
import { listVehicle, getVehicle, addVehicle, updateVehicle, delVehicle } from '@/api/system/vehicle';
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';

export default {
  components: {
    uniSection,
    uniPagination,
    uniIcons
  },
  data() {
    return {
      loading: false,
      showPopup: false,
      // 查询参数
      queryParams: {
        licensePlate: '',
        userNickName: '',
        pageNum: 1,
        pageSize: 10
      },
      // 车辆列表
      vehicleList: [],
      total: 0,
      // 弹窗标题
      formTitle: '',
      // 表单参数
      form: {
        id: undefined,
        licensePlate: '',
        vehicleType: '0',
        vehicleColor: '',
        vehicleBrand: '',
        vehicleModel: '',
        vehicleOwnerType: '3',
        status: '0',
        remark: ''
      },
      // 是否为编辑
      isEdit: false
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'permissions'
    ]),
    isAdmin() {
      const hasAdminRole = (this.roles || []).includes('admin')
      const hasSuperPermission = (this.permissions || []).includes('*:*:*')
      return hasAdminRole || hasSuperPermission
    }
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermi,
    /** 查询车辆列表 */
    async getList() {
      if (this.loading) return
      this.loading = true
      try {
        const res = await listVehicle(this.queryParams)
        this.vehicleList = res.rows || []
        this.total = res.total || 0
      } catch (error) {
        this.$modal.showToast('获取列表失败，请检查网络后重试')
        console.error('获取车辆列表失败:', error)
        this.vehicleList = []
        this.total = 0
      } finally {
        setTimeout(() => {
          this.loading = false
        }, 300)
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        licensePlate: '',
        userNickName: '',
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.formTitle = '添加车辆'
      this.isEdit = false
      this.showPopup = true
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset()
      this.loading = true
      try {
        const res = await getVehicle(row.id)
        this.form = res.data
        this.formTitle = '修改车辆'
        this.isEdit = true
        this.showPopup = true
      } catch (error) {
        this.$modal.showToast('获取车辆信息失败')
        console.error('获取车辆详情失败:', error)
      } finally {
        this.loading = false
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      uni.showModal({
        title: '提示',
        content: '确认要删除该车辆吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              this.loading = true
              await delVehicle(row.id)
              this.$modal.showToast('删除成功')
              await this.getList()
            } catch (error) {
              this.$modal.showToast('删除失败')
              console.error('删除车辆失败:', error)
            } finally {
              this.loading = false
            }
          }
        }
      })
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        licensePlate: '',
        vehicleType: '0',
        vehicleColor: '',
        vehicleBrand: '',
        vehicleModel: '',
        vehicleOwnerType: '3',
        status: '0',
        remark: ''
      }
    },
    /** 弹窗确认按钮 */
    async dialogConfirm() {
      if (this.loading) return
      
      // 表单验证
      if (!this.form.licensePlate.trim()) {
        this.$modal.showToast('请输入车牌号')
        return
      }
      if (!this.form.vehicleBrand.trim()) {
        this.$modal.showToast('请输入车辆品牌')
        return
      }
      
      this.loading = true
      uni.showLoading({
        title: this.isEdit ? '保存中...' : '添加中...'
      })
      
      try {
        if (this.isEdit) {
          await updateVehicle(this.form)
          this.$modal.showToast('修改成功')
        } else {
          await addVehicle(this.form)
          this.$modal.showToast('新增成功')
        }
        this.showPopup = false
        await this.getList()
      } catch (error) {
        const action = this.isEdit ? '修改' : '新增'
        this.$modal.showToast(`${action}失败，请稍后重试`)
        console.error(`${action}车辆失败:`, error)
      } finally {
        uni.hideLoading()
        setTimeout(() => {
          this.loading = false
        }, 500)
      }
    },
    /** 弹窗关闭按钮 */
    dialogClose() {
      this.showPopup = false
    },
    // 分页改变事件处理函数
    handlePageChange(e) {
      this.queryParams.pageNum = e.current
      this.getList()
    }
  }
}
</script>

<style lang="scss">
.vehicle-container {
  padding: 15px;
}

.search-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 15px;
  gap: 10px;

  .search-item {
    flex: 1;
    min-width: 200px;

    .search-input {
      width: 100%;
      height: 35px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 0 10px;
    }
  }

  .cu-btn {
    margin: 0;
    padding: 0 15px;
    height: 35px;
    line-height: 35px;
  }
}

.list-box {
  padding: 15px;
  min-height: 100px;

  .loading-box,
  .empty-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    color: #909399;
    font-size: 14px;
  }

  .list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);

    .item-main {
      flex: 1;
    }

    .item-title {
      margin-bottom: 10px;

      .plate-number {
        font-size: 16px;
        font-weight: bold;
        margin-right: 10px;
      }

      .vehicle-brand {
        color: #909399;
        font-size: 14px;
      }
    }

    .item-info {
      color: #606266;
      font-size: 14px;

      text {
        display: inline-block;
        margin-right: 15px;
        line-height: 24px;
      }
    }

    .item-actions {
      .cu-btn {
        margin: 0 5px;
      }
    }
  }
}
</style>
