<template>
  <view class="vehicle-container">
    <!-- 欢迎横幅 -->
    <view class="welcome-banner">
      <view class="banner-content">
        <view class="banner-title">车辆管理</view>
        <view class="banner-subtitle">选择您要管理的内容</view>
      </view>
    </view>

    <!-- 功能选择 -->
    <view class="modules-container">
      <view class="modules-grid">
        <!-- 车辆信息管理 -->
        <view class="module-item" @click="goToVehicleInfo" v-if="checkPermi(['asc:vehicle:list'])">
          <view class="module-icon">
            <uni-icons type="search" size="32" color="#409EFF"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">车辆信息管理</text>
            <text class="module-desc">车辆档案、车主信息管理</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 车牌机管理 -->
        <view class="module-item" @click="goToDeviceManage" v-if="checkPermi(['asc:device:manage'])">
          <view class="module-icon">
            <uni-icons type="gear" size="32" color="#E6A23C"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">车牌机管理</text>
            <text class="module-desc">车牌识别设备监控管理</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 无权限提示 -->
      <view class="no-permission" v-if="!hasAnyPermission">
        <uni-icons type="info" size="32" color="#ccc"></uni-icons>
        <text class="no-permission-text">您暂无车辆管理相关权限</text>
        <text class="no-permission-tip">请联系管理员开通权限</text>
      </view>
    </view>
  </view>
</template>

<script>
import { checkPermi } from '@/utils/permission';

export default {
  data() {
    return {
      
    }
  },
  computed: {
    hasAnyPermission() {
      return this.checkPermi(['asc:vehicle:list']) || this.checkPermi(['asc:device:manage']);
    }
  },
  methods: {
    checkPermi(permission) {
      return checkPermi(permission);
    },
    
    goToVehicleInfo() {
      console.log('跳转到车辆信息管理...');
      if (this.checkPermi(['asc:vehicle:list'])) {
        uni.navigateTo({
          url: '/subpackages/vehicle/info/index',
          success: () => {
            console.log('成功跳转到车辆信息管理页面');
          },
          fail: (err) => {
            console.error('跳转到车辆信息管理失败:', err);
            this.$modal.showToast('页面跳转失败');
          }
        });
      } else {
        console.log('无车辆信息管理权限');
        this.$modal.showToast('您没有访问该页面的权限');
      }
    },
    
    goToDeviceManage() {
      console.log('跳转到车牌机管理...');
      if (this.checkPermi(['asc:device:manage'])) {
        uni.navigateTo({
          url: '/subpackages/vehicle/device/index',
          success: () => {
            console.log('成功跳转到车牌机管理页面');
          },
          fail: (err) => {
            console.error('跳转到车牌机管理失败:', err);
            this.$modal.showToast('页面跳转失败');
          }
        });
      } else {
        console.log('无车牌机管理权限');
        this.$modal.showToast('您没有访问该页面的权限');
      }
    }
  },
  onLoad() {
    console.log('车辆管理页面加载');
    // 可以在这里添加页面初始化逻辑
  }
}
</script>

<style lang="scss">
.vehicle-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 欢迎横幅样式 */
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx;
  margin-bottom: 30rpx;

  .banner-content {
    text-align: center;
    color: white;

    .banner-title {
      font-size: 48rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
    }

    .banner-subtitle {
      font-size: 28rpx;
      opacity: 0.9;
    }
  }
}

/* 功能模块样式 */
.modules-container {
  padding: 0 30rpx;
}

.modules-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.module-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
  }

  .module-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 16rpx;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
  }

  .module-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    .module-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      line-height: 1.2;
    }

    .module-desc {
      font-size: 24rpx;
      color: #666;
      line-height: 1.3;
    }
  }

  .module-arrow {
    margin-left: 20rpx;
  }
}

/* 无权限提示样式 */
.no-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;

  .no-permission-text {
    font-size: 28rpx;
    color: #666;
    margin: 20rpx 0 10rpx;
  }

  .no-permission-tip {
    font-size: 24rpx;
    color: #999;
  }
}
</style>
