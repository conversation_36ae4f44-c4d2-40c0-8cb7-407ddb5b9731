<template>
  <view class="container">
    <uni-card class="view-title" title="隐私政策">
      <text class="uni-body view-content">
这里是高义钢铁有限公司的隐私政策内容。

请将实际的隐私政策文本替换此处的占位符内容。
      </text>
    </uni-card>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '隐私政策'
    });
  }
};
</script>

<style scoped>
page {
  background-color: #ffffff;
}

.container {
  padding: 15px;
}

.view-title {
  font-weight: bold;
}

.view-content {
  font-size: 26rpx;
  padding: 12px 5px 0;
  color: #333;
  line-height: 24px;
  font-weight: normal;
  white-space: pre-wrap; /* 保留换行符 */
}
</style>