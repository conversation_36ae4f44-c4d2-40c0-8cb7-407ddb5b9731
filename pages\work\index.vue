<template>
  <view class="work-container">
    <!-- 欢迎横幅 -->
    <view class="welcome-banner">
      <view class="banner-content">
        <view class="banner-title">工作台</view>
        <view class="banner-subtitle">选择您要使用的功能模块</view>
      </view>
    </view>

    <!-- 功能模块网格 -->
    <view class="modules-container">
      <view class="modules-grid">
        <!-- 能源管理 -->
        <view class="module-item" @click="gotoEms">
          <view class="module-icon">
            <uni-icons type="bars" size="32" color="#67C23A"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">能源管理</text>
            <text class="module-desc">实时数据监控</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 车辆管理 -->
        <view class="module-item" @click="gotoVehicle" v-if="hasVehiclePermission">
          <view class="module-icon">
            <uni-icons type="car" size="32" color="#409EFF"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">车辆管理</text>
            <text class="module-desc">车辆信息、车牌机管理</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 访客管理 -->
        <view class="module-item" @click="gotoVisitor" v-if="checkPermi(['asc:visitor:list'])">
          <view class="module-icon">
            <uni-icons type="person-add" size="32" color="#F56C6C"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">访客管理</text>
            <text class="module-desc">访客登记管理</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 数据图表 -->
        <view class="module-item" @click="gotoCharts">
          <view class="module-icon">
            <uni-icons type="chart" size="32" color="#9C27B0"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">数据图表</text>
            <text class="module-desc">数据可视化分析</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { checkPermi } from '@/utils/permission';
import { getDeviceStatus, openGate } from '@/api/system/device';

export default {
  data() {
    return {
      deviceList: []
    }
  },
  computed: {
    hasVehiclePermission() {
      const vehiclePermi = this.checkPermi(['asc:vehicle:list']);
      const devicePermi = this.checkPermi(['asc:device:manage']);
      const result = vehiclePermi || devicePermi;

      console.log('=== 车辆管理权限检查 ===');
      console.log('车辆权限(asc:vehicle:list):', vehiclePermi);
      console.log('设备权限(asc:device:manage):', devicePermi);
      console.log('最终结果:', result);
      console.log('用户权限列表:', this.$store.getters.permissions);
      console.log('用户角色列表:', this.$store.getters.roles);

      return result;
    }
  },
  methods: {
    checkPermi(permission) {
      return checkPermi(permission);
    },


    gotoEms() {
      uni.navigateTo({
        url: "/subpackages/ems/index",
      });
    },
    gotoVehicle() {
      console.log('跳转到车辆管理...');

      if (this.hasVehiclePermission) {
        uni.navigateTo({
          url: "/subpackages/vehicle/index",
          success: () => {
            console.log('成功跳转到车辆管理页面');
          },
          fail: (err) => {
            console.error('跳转到车辆管理页面失败:', err);
            this.$modal.showToast('页面跳转失败');
          }
        });
      } else {
        console.log('无车辆管理权限');
        this.$modal.showToast('您没有访问该页面的权限');
      }
    },

    gotoVisitor() {
      uni.navigateTo({
        url: "/subpackages/visitor/index",
        fail: (err) => {
          console.error('跳转到访客管理页面失败:', err);
          this.$modal.showToast('页面跳转失败');
        }
      });
    },

    gotoCharts() {
      uni.navigateTo({
        url: "/subpackages/charts/index",
        fail: (err) => {
          console.error('跳转到数据图表页面失败:', err);
          this.$modal.showToast('页面跳转失败');
        }
      });
    },

    // 获取设备状态
    getDeviceList() {
      getDeviceStatus().then(res => {
        this.deviceList = res.data;
        console.log('设备状态列表:', this.deviceList);
      }).catch(err => {
        console.error('获取设备状态失败:', err);
        this.$modal.showToast('获取设备状态失败');
      });
    },

    // 刷新设备状态
    refreshDeviceStatus() {
      this.$modal.showToast('正在刷新设备状态...');
      this.getDeviceList();
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '无心跳记录';

      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    },

    // 获取状态文本
    getStatusText(status) {
      if (status === 'CONNECTED') {
        return '在线';
      } else if (status === 'DISCONNECTED') {
        return '离线';
      } else {
        return '未知';
      }
    },

    // 处理开闸操作
    handleOpenGate(id) {
      uni.showModal({
        title: '确认操作',
        content: '确定要进行开闸操作吗？',
        success: res => {
          if (res.confirm) {
            this.$modal.showLoading('正在开闸...');
            openGate(id).then(res => {
              this.$modal.hideLoading();
              this.$modal.showSuccess('开闸成功');
            }).catch(err => {
              this.$modal.hideLoading();
              this.$modal.showError('开闸失败: ' + (err.message || '未知错误'));
            });
          }
        }
      });
    },
  },
  onLoad() {
    try {
      this.$store.dispatch('user/GetInfo').then(res => {
        console.log('获取用户信息成功:', res);
        // 获取设备列表
        if (checkPermi(['asc:device:manage'])) {
          this.getDeviceList();
        }
      }).catch(error => {
        console.error('获取用户信息失败:', error);
        this.$modal.showToast('获取用户信息失败');
      });
    } catch (error) {
      console.error('执行 onLoad 时发生错误:', error);
      this.$modal.showToast('系统错误');
    }
  }
}
</script>

<style lang="scss">
/* #ifndef APP-NVUE */
page {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #fff;
  min-height: 100%;
  height: auto;
}

view {
  font-size: 14px;
  line-height: inherit;
}

/* #endif */

/* 欢迎横幅样式 */
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx;
  margin-bottom: 30rpx;

  .banner-content {
    text-align: center;
    color: white;

    .banner-title {
      font-size: 48rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
    }

    .banner-subtitle {
      font-size: 28rpx;
      opacity: 0.9;
    }
  }
}

/* 功能模块样式 */
.modules-container {
  padding: 0 30rpx;
}

.modules-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.module-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
  }

  .module-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 16rpx;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
  }

  .module-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    .module-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      line-height: 1.2;
    }

    .module-desc {
      font-size: 24rpx;
      color: #666;
      line-height: 1.3;
    }
  }

  .module-arrow {
    margin-left: 20rpx;
  }
}



/* 设备控制样式 */
.device-control-container {
  padding: 10px;
}

.device-info {
  margin-bottom: 10px;
}

.device-item {
  display: flex;
  margin-bottom: 5px;
}

.label {
  font-weight: bold;
  min-width: 100px;
}

.device-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 10px;
}

.refresh-btn {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}


</style>
