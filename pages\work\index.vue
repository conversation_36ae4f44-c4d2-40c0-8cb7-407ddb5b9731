<template>
  <view class="work-container">
    <!-- 轮播图 -->
    <uni-swiper-dot class="uni-swiper-dot-box" :info="data" :current="current" field="content">
      <swiper class="swiper-box" :current="swiperDotIndex" @change="changeSwiper">
        <swiper-item v-for="(item, index) in data" :key="index">
          <view class="swiper-item" @click="clickBannerItem(item)">
            <image :src="item.image" mode="aspectFill" :draggable="false" />
          </view>
        </swiper-item>
      </swiper>
    </uni-swiper-dot>

    <!-- 生产管理 Section -->
    <uni-section title="生产管理" type="line"></uni-section>
    <view class="section-container">
      <view class="grid-item-box" @click="gotoEms">
        <uni-icons type="person-filled" size="30"></uni-icons>
        <text class="text">实时数据</text>
      </view>
    </view>

    <!-- 车辆门禁管理 Section -->
    <uni-section title="车辆门禁管理" type="line"></uni-section>
    <view class="section-container">
      <view class="grid-item-box" @click="gotoVehicle" v-if="checkPermi(['asc:vehicle:list'])">
        <uni-icons type="fire-filled" size="30"></uni-icons>
        <text class="text">车辆管理</text>
      </view>
      <view class="grid-item-box" @click="gotoDeviceManage" v-if="checkPermi(['asc:device:manage'])">
        <uni-icons type="settings-filled" size="30"></uni-icons>
        <text class="text">设备管理</text>
      </view>
    </view>

    <!-- 一键开闸区域 -->
    <!-- <uni-section title="一键开闸" type="line" v-if="checkPermi(['asc:device:manage'])"></uni-section>
    <view class="device-control-container" v-if="checkPermi(['asc:device:manage'])">
      <uni-card v-for="device in deviceList" :key="device.id" :title="device.deviceName" :extra="getStatusText(device.connectionStatus)">
        <view class="device-info">
          <view class="device-item">
            <text class="label">设备编码：</text>
            <text class="value">{{device.deviceCode}}</text>
          </view>
          <view class="device-item">
            <text class="label">设备IP：</text>
            <text class="value">{{device.deviceIp}}</text>
          </view>
        </view>
        <view class="device-actions">
          <button type="primary" size="mini" @click="handleOpenGate(device.id)" :disabled="device.connectionStatus !== 'CONNECTED'">开闸</button>
        </view>
      </uni-card>
      <view class="refresh-btn">
        <button type="default" size="mini" @click="refreshDeviceStatus">刷新状态</button>
      </view>
    </view> -->
  </view>
</template>

<script>
import { mapState } from 'vuex';
import { checkRole, checkPermi } from '@/utils/permission';
import { getDeviceStatus, openGate } from '@/api/system/device';

export default {
  data() {
    return {
      current: 0,
      swiperDotIndex: 0,
      deviceList: [],
      data: [
        // {
        //          image: '/static/images/banner/banner01.jpg'
        //        },
        {
          image: '/static/images/banner/banner02.jpg'
        },
        {
          image: '/static/images/banner/banner03.jpg'
        }
      ]
    }
  },
  methods: {
    checkPermi(permission) {
      return checkPermi(permission);
    },
    clickBannerItem(item) {
      console.info(item)
    },

    changeSwiper(e) {
      this.current = e.detail.current
    },

    changeGrid(e) {
      this.$modal.showToast('模块建设中~')
      console.log(e)
    },

    gotoEms() {
      uni.navigateTo({
        url: "/subpackages/ems/index",
      });
    },
    gotoVehicle() {
      console.log('检查车辆管理权限...');

      if (checkPermi(['asc:vehicle:list'])) {
        uni.navigateTo({
          url: "/subpackages/vehicle/index",
          success: () => {
            console.log('成功跳转到车辆管理页面');
          },
          fail: (err) => {
            console.error('跳转到车辆管理页面失败:', err);
            this.$modal.showToast('页面跳转失败');
          }
        });
      } else {
        console.log('无车辆管理权限');
        this.$modal.showToast('您没有访问该页面的权限');
      }
    },

    gotoDeviceManage() {
      uni.navigateTo({
        url: "/subpackages/device/index",
        fail: (err) => {
          console.error('跳转到设备管理页面失败:', err);
          this.$modal.showToast('页面跳转失败');
        }
      });
    },

    // 获取设备状态
    getDeviceList() {
      getDeviceStatus().then(res => {
        this.deviceList = res.data;
        console.log('设备状态列表:', this.deviceList);
      }).catch(err => {
        console.error('获取设备状态失败:', err);
        this.$modal.showToast('获取设备状态失败');
      });
    },

    // 刷新设备状态
    refreshDeviceStatus() {
      this.$modal.showToast('正在刷新设备状态...');
      this.getDeviceList();
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '无心跳记录';

      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    },

    // 获取状态文本
    getStatusText(status) {
      if (status === 'CONNECTED') {
        return '在线';
      } else if (status === 'DISCONNECTED') {
        return '离线';
      } else {
        return '未知';
      }
    },

    // 处理开闸操作
    handleOpenGate(id) {
      uni.showModal({
        title: '确认操作',
        content: '确定要进行开闸操作吗？',
        success: res => {
          if (res.confirm) {
            this.$modal.showLoading('正在开闸...');
            openGate(id).then(res => {
              this.$modal.hideLoading();
              this.$modal.showSuccess('开闸成功');
            }).catch(err => {
              this.$modal.hideLoading();
              this.$modal.showError('开闸失败: ' + (err.message || '未知错误'));
            });
          }
        }
      });
    },
  },
  onLoad() {
    try {
      this.$store.dispatch('user/GetInfo').then(res => {
        console.log('获取用户信息成功:', res);
        // 获取设备列表
        if (checkPermi(['asc:device:manage'])) {
          this.getDeviceList();
        }
      }).catch(error => {
        console.error('获取用户信息失败:', error);
        this.$modal.showToast('获取用户信息失败');
      });
    } catch (error) {
      console.error('执行 onLoad 时发生错误:', error);
      this.$modal.showToast('系统错误');
    }
  },
  computed: {
    // 由于我们使用 checkPermi 函数处理所有权限检查，不再需要直接映射状态
  }
}
</script>

<style lang="scss">
/* #ifndef APP-NVUE */
page {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #fff;
  min-height: 100%;
  height: auto;
}

view {
  font-size: 14px;
  line-height: inherit;
}

/* #endif */

.text {
  text-align: center;
  font-size: 26rpx;
  margin-top: 10rpx;
}

.section-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 10px;
}

.grid-item-box {
  flex: 0 0 auto;
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
}

.uni-margin-wrap {
  width: 690rpx;
  width: 100%;
}

.swiper {
  height: 300rpx;
}

.swiper-box {
  height: 150px;
}

.swiper-item {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  height: 300rpx;
  line-height: 300rpx;
}

@media screen and (min-width: 500px) {
  .uni-swiper-dot-box {
    width: 400px;
    /* #ifndef APP-NVUE */
    margin: 0 auto;
    /* #endif */
    margin-top: 8px;
  }

  .image {
    width: 100%;
  }
}

/* 设备控制样式 */
.device-control-container {
  padding: 10px;
}

.device-info {
  margin-bottom: 10px;
}

.device-item {
  display: flex;
  margin-bottom: 5px;
}

.label {
  font-weight: bold;
  min-width: 100px;
}

.device-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 10px;
}

.refresh-btn {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}
</style>
