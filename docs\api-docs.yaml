openapi: 3.0.1
info:
  title: OpenAPI definition
  version: v0
servers:
- url: http://localhost:8088
  description: Generated server url
paths:
  /tool/gen:
    put:
      tags:
      - gen-controller
      operationId: editSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenTable'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /test/user/update:
    put:
      tags:
      - test-controller
      operationId: update
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserEntity'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RString'
  /system/user:
    put:
      tags:
      - sys-user-controller
      operationId: edit
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysUser'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-user-controller
      operationId: add
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysUser'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/resetPwd:
    put:
      tags:
      - sys-user-controller
      operationId: resetPwd
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysUser'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/profile:
    get:
      tags:
      - sys-profile-controller
      operationId: profile
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    put:
      tags:
      - sys-profile-controller
      operationId: updateProfile
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysUser'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/profile/updatePwd:
    put:
      tags:
      - sys-profile-controller
      operationId: updatePwd
      parameters:
      - name: oldPassword
        in: query
        required: true
        schema:
          type: string
      - name: newPassword
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/changeStatus:
    put:
      tags:
      - sys-user-controller
      operationId: changeStatus
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysUser'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/authRole:
    put:
      tags:
      - sys-user-controller
      operationId: insertAuthRole
      parameters:
      - name: userId
        in: query
        required: true
        schema:
          type: integer
          format: int64
      - name: roleIds
        in: query
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role:
    put:
      tags:
      - sys-role-controller
      operationId: edit_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysRole'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-role-controller
      operationId: add_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysRole'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role/dataScope:
    put:
      tags:
      - sys-role-controller
      operationId: dataScope
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysRole'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role/changeStatus:
    put:
      tags:
      - sys-role-controller
      operationId: changeStatus_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysRole'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role/authUser/selectAll:
    put:
      tags:
      - sys-role-controller
      operationId: selectAuthUserAll
      parameters:
      - name: roleId
        in: query
        required: true
        schema:
          type: integer
          format: int64
      - name: userIds
        in: query
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role/authUser/cancel:
    put:
      tags:
      - sys-role-controller
      operationId: cancelAuthUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysUserRole'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role/authUser/cancelAll:
    put:
      tags:
      - sys-role-controller
      operationId: cancelAuthUserAll
      parameters:
      - name: roleId
        in: query
        required: true
        schema:
          type: integer
          format: int64
      - name: userIds
        in: query
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/post:
    put:
      tags:
      - sys-post-controller
      operationId: edit_2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysPost'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-post-controller
      operationId: add_2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysPost'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/notice:
    put:
      tags:
      - sys-notice-controller
      operationId: edit_3
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysNotice'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-notice-controller
      operationId: add_3
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysNotice'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/menu:
    put:
      tags:
      - sys-menu-controller
      operationId: edit_4
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysMenu'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-menu-controller
      operationId: add_4
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysMenu'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dict/type:
    put:
      tags:
      - sys-dict-type-controller
      operationId: edit_5
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysDictType'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-dict-type-controller
      operationId: add_5
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysDictType'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dict/data:
    put:
      tags:
      - sys-dict-data-controller
      operationId: edit_6
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysDictData'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-dict-data-controller
      operationId: add_6
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysDictData'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dept:
    put:
      tags:
      - sys-dept-controller
      operationId: edit_7
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysDept'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-dept-controller
      operationId: add_7
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysDept'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/config:
    put:
      tags:
      - sys-config-controller
      operationId: edit_8
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysConfig'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-config-controller
      operationId: add_8
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysConfig'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/office:
    put:
      tags:
      - sys-office-controller
      operationId: edit_9
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysOffice'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-office-controller
      operationId: add_11
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysOffice'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/officeApply:
    put:
      tags:
      - sys-office-apply-controller
      operationId: edit_10
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysOfficeApply'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-office-apply-controller
      operationId: add_12
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysOfficeApply'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/officeApply/return:
    put:
      tags:
      - sys-office-apply-controller
      operationId: returnOffice
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysOfficeApply'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/officeApply/receive:
    put:
      tags:
      - sys-office-apply-controller
      operationId: receive
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysOfficeApply'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/officeApply/approve:
    put:
      tags:
      - sys-office-apply-controller
      operationId: approve
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysOfficeApply'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/asset:
    put:
      tags:
      - sys-asset-controller
      operationId: edit_11
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysAsset'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-asset-controller
      operationId: add_13
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysAsset'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/assetRecord:
    put:
      tags:
      - sys-asset-record-controller
      operationId: edit_12
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysAssetRecord'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-asset-record-controller
      operationId: add_14
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysAssetRecord'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/job:
    put:
      tags:
      - sys-job-controller
      operationId: edit_13
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysJob'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - sys-job-controller
      operationId: add_15
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysJob'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/job/run:
    put:
      tags:
      - sys-job-controller
      operationId: run
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysJob'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/job/changeStatus:
    put:
      tags:
      - sys-job-controller
      operationId: changeStatus_2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SysJob'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /ems/tags:
    put:
      tags:
      - ems-controller
      operationId: edit_14
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Tag'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - ems-controller
      operationId: add_16
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Tag'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /ems/org:
    put:
      tags:
      - ems-org-controller
      operationId: edit_15
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EMSOrg'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - ems-org-controller
      operationId: add_17
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EMSOrg'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/visitor:
    put:
      tags:
      - visitor-controller
      operationId: edit_16
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Visitor'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - visitor-controller
      operationId: add_19
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Visitor'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/visitor/checkout/{id}:
    put:
      tags:
      - visitor-controller
      operationId: checkout
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/video:
    put:
      tags:
      - video-controller
      operationId: edit_17
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Video'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - video-controller
      operationId: add_20
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Video'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/verification:
    put:
      tags:
      - visitor-verification-controller
      operationId: edit_18
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VisitorVerification'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - visitor-verification-controller
      operationId: add_21
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VisitorVerification'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicle:
    put:
      tags:
      - vehicle-controller
      operationId: edit_19
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Vehicle'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - vehicle-controller
      operationId: add_22
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Vehicle'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicleArea:
    put:
      tags:
      - vehicle-area-controller
      operationId: edit_20
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VehicleArea'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - vehicle-area-controller
      operationId: add_23
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/VehicleArea'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicle/area/{vehicleId}:
    put:
      tags:
      - vehicle-controller
      operationId: setAreaPermissions
      parameters:
      - name: vehicleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicle/area/batch:
    put:
      tags:
      - vehicle-controller
      operationId: batchSetAreaPermissions
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: object
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/record:
    put:
      tags:
      - license-plate-record-controller
      operationId: edit_21
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LicensePlateRecord'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - license-plate-record-controller
      operationId: add_24
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LicensePlateRecord'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/printer:
    put:
      tags:
      - printer-config-controller
      operationId: edit_22
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrinterConfig'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - printer-config-controller
      operationId: add_25
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrinterConfig'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/printer/setDefault/{id}:
    put:
      tags:
      - printer-config-controller
      operationId: setDefault
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/device:
    put:
      tags:
      - license-plate-device-controller
      operationId: edit_23
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LicensePlateDevice'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - license-plate-device-controller
      operationId: add_26
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LicensePlateDevice'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/area:
    put:
      tags:
      - area-controller
      operationId: edit_24
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Area'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - area-controller
      operationId: add_27
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Area'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /archives/materialclass:
    put:
      tags:
      - material-class-controller
      operationId: update_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaterialClass'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - material-class-controller
      operationId: add_28
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaterialClass'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /archives/material:
    put:
      tags:
      - material-controller
      operationId: update_2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Material'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - material-controller
      operationId: add_29
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Material'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /api:
    put:
      tags:
      - api-key-controller
      operationId: update_3
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiKey'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - api-key-controller
      operationId: add_30
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiKey'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /api/reset/{id}:
    put:
      tags:
      - api-key-controller
      operationId: reset
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /api/permission:
    put:
      tags:
      - api-permission-controller
      operationId: edit_25
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiPermission'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    post:
      tags:
      - api-permission-controller
      operationId: add_31
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiPermission'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /tool/gen/importTable:
    post:
      tags:
      - gen-controller
      operationId: importTableSave
      parameters:
      - name: tables
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /test/user/save:
    post:
      tags:
      - test-controller
      operationId: save
      parameters:
      - name: userId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: username
        in: query
        required: false
        schema:
          type: string
      - name: password
        in: query
        required: false
        schema:
          type: string
      - name: mobile
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RString'
  /system/user/profile/avatar:
    post:
      tags:
      - sys-profile-controller
      operationId: avatar
      requestBody:
        content:
          application/json:
            schema:
              required:
              - avatarfile
              type: object
              properties:
                avatarfile:
                  type: string
                  format: binary
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/importTemplate:
    post:
      tags:
      - sys-user-controller
      operationId: importTemplate
      responses:
        "200":
          description: OK
  /system/user/importData:
    post:
      tags:
      - sys-user-controller
      operationId: importData
      parameters:
      - name: updateSupport
        in: query
        required: true
        schema:
          type: boolean
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/export:
    post:
      tags:
      - sys-user-controller
      operationId: export
      parameters:
      - name: userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: userName
        in: query
        required: false
        schema:
          type: string
      - name: nickName
        in: query
        required: false
        schema:
          type: string
      - name: email
        in: query
        required: false
        schema:
          type: string
      - name: phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: sex
        in: query
        required: false
        schema:
          type: string
      - name: avatar
        in: query
        required: false
        schema:
          type: string
      - name: password
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: loginIp
        in: query
        required: false
        schema:
          type: string
      - name: loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: dept.email
        in: query
        required: false
        schema:
          type: string
      - name: dept.status
        in: query
        required: false
        schema:
          type: string
      - name: dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /system/role/export:
    post:
      tags:
      - sys-role-controller
      operationId: export_1
      parameters:
      - name: roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: roleName
        in: query
        required: false
        schema:
          type: string
      - name: roleKey
        in: query
        required: false
        schema:
          type: string
      - name: roleSort
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: dataScope
        in: query
        required: false
        schema:
          type: string
      - name: menuCheckStrictly
        in: query
        required: false
        schema:
          type: boolean
      - name: deptCheckStrictly
        in: query
        required: false
        schema:
          type: boolean
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: flag
        in: query
        required: false
        schema:
          type: boolean
      - name: menuIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: deptIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: permissions
        in: query
        required: false
        schema:
          uniqueItems: true
          type: array
          items:
            type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /system/post/export:
    post:
      tags:
      - sys-post-controller
      operationId: export_2
      parameters:
      - name: postId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: postCode
        in: query
        required: false
        schema:
          type: string
      - name: postName
        in: query
        required: false
        schema:
          type: string
      - name: postSort
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: flag
        in: query
        required: false
        schema:
          type: boolean
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /system/dict/type/export:
    post:
      tags:
      - sys-dict-type-controller
      operationId: export_3
      parameters:
      - name: dictId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dictName
        in: query
        required: false
        schema:
          type: string
      - name: dictType
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /system/dict/data/export:
    post:
      tags:
      - sys-dict-data-controller
      operationId: export_4
      parameters:
      - name: dictCode
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dictSort
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dictLabel
        in: query
        required: false
        schema:
          type: string
      - name: dictValue
        in: query
        required: false
        schema:
          type: string
      - name: dictType
        in: query
        required: false
        schema:
          type: string
      - name: cssClass
        in: query
        required: false
        schema:
          type: string
      - name: listClass
        in: query
        required: false
        schema:
          type: string
      - name: isDefault
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /system/config/export:
    post:
      tags:
      - sys-config-controller
      operationId: export_5
      parameters:
      - name: configId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: configName
        in: query
        required: false
        schema:
          type: string
      - name: configKey
        in: query
        required: false
        schema:
          type: string
      - name: configValue
        in: query
        required: false
        schema:
          type: string
      - name: configType
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /reportform/list:
    post:
      tags:
      - report-form-controller
      operationId: listReportform
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /register:
    post:
      tags:
      - sys-register-controller
      operationId: register
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterBody'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/person/export:
    post:
      tags:
      - out-sourcing-person-controller
      operationId: export_6
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgName
        in: query
        required: false
        schema:
          type: string
      - name: org.id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: org.orgName
        in: query
        required: false
        schema:
          type: string
      - name: org.contactPerson
        in: query
        required: false
        schema:
          type: string
      - name: org.contactPhone
        in: query
        required: false
        schema:
          type: string
      - name: org.enable
        in: query
        required: false
        schema:
          type: string
          format: byte
      - name: name
        in: query
        required: false
        schema:
          type: string
      - name: addr
        in: query
        required: false
        schema:
          type: string
      - name: gender
        in: query
        required: false
        schema:
          type: string
      - name: identityCard
        in: query
        required: false
        schema:
          type: string
      - name: position
        in: query
        required: false
        schema:
          type: string
      - name: phone
        in: query
        required: false
        schema:
          type: string
      - name: validity
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: is_validity
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/person/edit:
    post:
      tags:
      - out-sourcing-person-controller
      operationId: edit_26
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgName
        in: query
        required: false
        schema:
          type: string
      - name: org.id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: org.orgName
        in: query
        required: false
        schema:
          type: string
      - name: org.contactPerson
        in: query
        required: false
        schema:
          type: string
      - name: org.contactPhone
        in: query
        required: false
        schema:
          type: string
      - name: org.enable
        in: query
        required: false
        schema:
          type: string
          format: byte
      - name: name
        in: query
        required: false
        schema:
          type: string
      - name: addr
        in: query
        required: false
        schema:
          type: string
      - name: gender
        in: query
        required: false
        schema:
          type: string
      - name: identityCard
        in: query
        required: false
        schema:
          type: string
      - name: position
        in: query
        required: false
        schema:
          type: string
      - name: phone
        in: query
        required: false
        schema:
          type: string
      - name: validity
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: is_validity
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/person/del:
    post:
      tags:
      - out-sourcing-person-controller
      operationId: del
      parameters:
      - name: ids
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/person/add:
    post:
      tags:
      - out-sourcing-person-controller
      operationId: add_9
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgName
        in: query
        required: false
        schema:
          type: string
      - name: org.id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: org.orgName
        in: query
        required: false
        schema:
          type: string
      - name: org.contactPerson
        in: query
        required: false
        schema:
          type: string
      - name: org.contactPhone
        in: query
        required: false
        schema:
          type: string
      - name: org.enable
        in: query
        required: false
        schema:
          type: string
          format: byte
      - name: name
        in: query
        required: false
        schema:
          type: string
      - name: addr
        in: query
        required: false
        schema:
          type: string
      - name: gender
        in: query
        required: false
        schema:
          type: string
      - name: identityCard
        in: query
        required: false
        schema:
          type: string
      - name: position
        in: query
        required: false
        schema:
          type: string
      - name: phone
        in: query
        required: false
        schema:
          type: string
      - name: validity
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: is_validity
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/org/list:
    post:
      tags:
      - out-sourcing-org-controller
      operationId: list
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgName
        in: query
        required: false
        schema:
          type: string
      - name: contactPerson
        in: query
        required: false
        schema:
          type: string
      - name: contactPhone
        in: query
        required: false
        schema:
          type: string
      - name: enable
        in: query
        required: false
        schema:
          type: string
          format: byte
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /outsourcing/org/importData:
    post:
      tags:
      - out-sourcing-org-controller
      operationId: importData_1
      parameters:
      - name: updateSupport
        in: query
        required: true
        schema:
          type: boolean
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/org/export:
    post:
      tags:
      - out-sourcing-org-controller
      operationId: export_7
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgName
        in: query
        required: false
        schema:
          type: string
      - name: contactPerson
        in: query
        required: false
        schema:
          type: string
      - name: contactPhone
        in: query
        required: false
        schema:
          type: string
      - name: enable
        in: query
        required: false
        schema:
          type: string
          format: byte
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/org/edit:
    post:
      tags:
      - out-sourcing-org-controller
      operationId: edit_27
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgName
        in: query
        required: false
        schema:
          type: string
      - name: contactPerson
        in: query
        required: false
        schema:
          type: string
      - name: contactPhone
        in: query
        required: false
        schema:
          type: string
      - name: enable
        in: query
        required: false
        schema:
          type: string
          format: byte
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/org/del:
    post:
      tags:
      - out-sourcing-org-controller
      operationId: del_1
      parameters:
      - name: ids
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/org/add:
    post:
      tags:
      - out-sourcing-org-controller
      operationId: add_10
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgName
        in: query
        required: false
        schema:
          type: string
      - name: contactPerson
        in: query
        required: false
        schema:
          type: string
      - name: contactPhone
        in: query
        required: false
        schema:
          type: string
      - name: enable
        in: query
        required: false
        schema:
          type: string
          format: byte
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/officeRecord/export:
    post:
      tags:
      - sys-office-record-controller
      operationId: export_8
      parameters:
      - name: recordId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: applyId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: applyNo
        in: query
        required: false
        schema:
          type: string
      - name: detailId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: officeId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: officeName
        in: query
        required: false
        schema:
          type: string
      - name: recordType
        in: query
        required: false
        schema:
          type: string
      - name: recordNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: recordTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: operName
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /office/officeApply/export:
    post:
      tags:
      - sys-office-apply-controller
      operationId: export_9
      parameters:
      - name: applyId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: applyNo
        in: query
        required: false
        schema:
          type: string
      - name: applyUser
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: applyUserName
        in: query
        required: false
        schema:
          type: string
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: applyTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: approveUser
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: approveUserName
        in: query
        required: false
        schema:
          type: string
      - name: approveTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: approveRemark
        in: query
        required: false
        schema:
          type: string
      - name: applyDetailList
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysOfficeApplyDetail'
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /office/office/export:
    post:
      tags:
      - sys-office-controller
      operationId: export_10
      parameters:
      - name: officeId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: officeName
        in: query
        required: false
        schema:
          type: string
      - name: category
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: stock
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: unit
        in: query
        required: false
        schema:
          type: string
      - name: remark
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      responses:
        "200":
          description: OK
  /office/asset/stats:
    post:
      tags:
      - sys-asset-controller
      operationId: getAssetStats
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssetStatsRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/asset/importTemplate:
    post:
      tags:
      - sys-asset-controller
      operationId: importTemplate_1
      responses:
        "200":
          description: OK
  /office/asset/importData:
    post:
      tags:
      - sys-asset-controller
      operationId: importData_2
      parameters:
      - name: updateSupport
        in: query
        required: true
        schema:
          type: boolean
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/asset/export:
    post:
      tags:
      - sys-asset-controller
      operationId: export_11
      parameters:
      - name: assetId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: assetCode
        in: query
        required: true
        schema:
          maxLength: 30
          minLength: 1
          type: string
      - name: assetName
        in: query
        required: true
        schema:
          maxLength: 30
          minLength: 1
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: specification
        in: query
        required: false
        schema:
          type: string
      - name: brand
        in: query
        required: false
        schema:
          type: string
      - name: purchaseDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: purchasePrice
        in: query
        required: false
        schema:
          type: number
      - name: expectedLifeYears
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: depreciationMethod
        in: query
        required: false
        schema:
          type: string
      - name: currentValue
        in: query
        required: false
        schema:
          type: number
      - name: accumulatedDepreciation
        in: query
        required: false
        schema:
          type: number
      - name: lastDepreciationDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: useUserId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: useUser
        in: query
        required: false
        schema:
          type: string
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: location
        in: query
        required: false
        schema:
          type: string
      - name: remark
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      responses:
        "200":
          description: OK
  /office/asset/depreciation/update/{assetId}:
    post:
      tags:
      - sys-asset-depreciation-controller
      operationId: updateDepreciation
      parameters:
      - name: assetId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/asset/depreciation/calculate/batch:
    post:
      tags:
      - sys-asset-depreciation-controller
      operationId: calculateBatchDepreciation
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/operlog/export:
    post:
      tags:
      - sys-operlog-controller
      operationId: export_12
      parameters:
      - name: operId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: title
        in: query
        required: false
        schema:
          type: string
      - name: businessType
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: businessTypes
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int32
      - name: method
        in: query
        required: false
        schema:
          type: string
      - name: requestMethod
        in: query
        required: false
        schema:
          type: string
      - name: operatorType
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: operName
        in: query
        required: false
        schema:
          type: string
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: operUrl
        in: query
        required: false
        schema:
          type: string
      - name: operIp
        in: query
        required: false
        schema:
          type: string
      - name: operLocation
        in: query
        required: false
        schema:
          type: string
      - name: operParam
        in: query
        required: false
        schema:
          type: string
      - name: jsonResult
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: errorMsg
        in: query
        required: false
        schema:
          type: string
      - name: operTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: costTime
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /monitor/logininfor/export:
    post:
      tags:
      - sys-logininfor-controller
      operationId: export_13
      parameters:
      - name: infoId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: userName
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: ipaddr
        in: query
        required: false
        schema:
          type: string
      - name: loginLocation
        in: query
        required: false
        schema:
          type: string
      - name: browser
        in: query
        required: false
        schema:
          type: string
      - name: os
        in: query
        required: false
        schema:
          type: string
      - name: msg
        in: query
        required: false
        schema:
          type: string
      - name: loginTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /monitor/jobLog/export:
    post:
      tags:
      - sys-job-log-controller
      operationId: export_14
      parameters:
      - name: jobLogId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: jobName
        in: query
        required: false
        schema:
          type: string
      - name: jobGroup
        in: query
        required: false
        schema:
          type: string
      - name: invokeTarget
        in: query
        required: false
        schema:
          type: string
      - name: jobMessage
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: exceptionInfo
        in: query
        required: false
        schema:
          type: string
      - name: startTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: stopTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /monitor/job/export:
    post:
      tags:
      - sys-job-controller
      operationId: export_15
      parameters:
      - name: jobId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: jobName
        in: query
        required: false
        schema:
          type: string
      - name: jobGroup
        in: query
        required: false
        schema:
          type: string
      - name: invokeTarget
        in: query
        required: false
        schema:
          type: string
      - name: cronExpression
        in: query
        required: false
        schema:
          type: string
      - name: misfirePolicy
        in: query
        required: false
        schema:
          type: string
      - name: concurrent
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /login:
    post:
      tags:
      - sys-login-controller
      operationId: login
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginBody'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /hr/recruit:
    post:
      tags:
      - recruit-controller
      operationId: toNc
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /gy-api/material/list:
    post:
      tags:
      - api-material-controller
      operationId: list_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiParam'
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /ems/tags/querybyname:
    post:
      tags:
      - ems-controller
      operationId: queryByName
      parameters:
      - name: username
        in: query
        required: false
        schema:
          type: string
          default: ""
      - name: deptIdsString
        in: query
        required: false
        schema:
          type: string
          default: ""
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /ems/tags/listOrg:
    post:
      tags:
      - ems-controller
      operationId: list_2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EMSOrg'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /ems/tags/importTemplate:
    post:
      tags:
      - ems-controller
      operationId: importTemplate_2
      responses:
        "200":
          description: OK
  /ems/tags/importData:
    post:
      tags:
      - ems-controller
      operationId: importData_3
      parameters:
      - name: updateSupport
        in: query
        required: true
        schema:
          type: boolean
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /ems/tags/getTagChart:
    post:
      tags:
      - ems-controller
      operationId: getTagChart
      parameters:
      - name: tagId
        in: query
        required: true
        schema:
          type: string
      - name: chartType
        in: query
        required: false
        schema:
          type: string
          default: line
      - name: period
        in: query
        required: false
        schema:
          type: string
          default: hour
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RUniAppLineChartBo'
  /ems/tags/export:
    post:
      tags:
      - ems-controller
      operationId: export_16
      parameters:
      - name: tagId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: tagName
        in: query
        required: false
        schema:
          type: string
      - name: tagAddr
        in: query
        required: false
        schema:
          type: string
      - name: tagType
        in: query
        required: false
        schema:
          type: string
      - name: tagOrgid
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: tagQueryInterval
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: tagCurrentTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: tagCurrentVal
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /ems/org/tree:
    post:
      tags:
      - ems-org-controller
      operationId: listMaterialClassTree
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EMSOrg'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /ems/org/export:
    post:
      tags:
      - ems-org-controller
      operationId: export_17
      parameters:
      - name: orgId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: orgType
        in: query
        required: false
        schema:
          type: string
      - name: orgName
        in: query
        required: false
        schema:
          type: string
      - name: parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: ancestors
        in: query
        required: false
        schema:
          type: string
      - name: children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/EMSOrg'
      responses:
        "200":
          description: OK
  /common/uploads:
    post:
      tags:
      - common-controller
      operationId: uploadFiles
      parameters:
      - name: files
        in: query
        required: true
        schema:
          type: array
          items:
            type: string
            format: binary
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /common/upload:
    post:
      tags:
      - common-controller
      operationId: uploadFile
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /cleanly/list:
    post:
      tags:
      - cleanly-controller
      operationId: list_3
      parameters:
      - name: carId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: carNum
        in: query
        required: false
        schema:
          type: string
      - name: carType
        in: query
        required: false
        schema:
          type: string
      - name: engineNum
        in: query
        required: false
        schema:
          type: string
      - name: emissionNum
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /cleanly/edit:
    post:
      tags:
      - cleanly-controller
      operationId: editSave_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Cars'
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /cleanly/del:
    post:
      tags:
      - cleanly-controller
      operationId: del_2
      parameters:
      - name: ids
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /cleanly/add:
    post:
      tags:
      - cleanly-controller
      operationId: add_18
      parameters:
      - name: carId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: carNum
        in: query
        required: false
        schema:
          type: string
      - name: carType
        in: query
        required: false
        schema:
          type: string
      - name: engineNum
        in: query
        required: false
        schema:
          type: string
      - name: emissionNum
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /card/querybyname:
    post:
      tags:
      - card-controller
      operationId: queryByName_1
      parameters:
      - name: username
        in: query
        required: false
        schema:
          type: string
          default: ""
      - name: deptIdsString
        in: query
        required: false
        schema:
          type: string
          default: ""
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /card/querybyid:
    post:
      tags:
      - card-controller
      operationId: queryById
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: string
          default: ""
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /card/querybyfuzzname:
    post:
      tags:
      - card-controller
      operationId: queryByFuzzName
      parameters:
      - name: username
        in: query
        required: false
        schema:
          type: string
          default: ""
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /card/group/update:
    post:
      tags:
      - card-controller
      operationId: groupUpdate
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupUpdateDomain'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/visitor/reprint/{id}:
    post:
      tags:
      - visitor-controller
      operationId: getReprintTicketContent
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/visitor/print/{id}:
    post:
      tags:
      - visitor-controller
      operationId: getPrintTicketContent
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/visitor/export:
    post:
      tags:
      - visitor-controller
      operationId: export_18
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: name
        in: query
        required: false
        schema:
          type: string
      - name: idCard
        in: query
        required: false
        schema:
          type: string
      - name: phone
        in: query
        required: false
        schema:
          type: string
      - name: company
        in: query
        required: false
        schema:
          type: string
      - name: purpose
        in: query
        required: false
        schema:
          type: string
      - name: visitTo
        in: query
        required: false
        schema:
          type: string
      - name: visitToDept
        in: query
        required: false
        schema:
          type: string
      - name: visitToUserId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: checkInTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: checkOutTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: idCardVerified
        in: query
        required: false
        schema:
          type: string
      - name: verificationMethod
        in: query
        required: false
        schema:
          type: string
      - name: idPhotoRef
        in: query
        required: false
        schema:
          type: string
      - name: qrCode
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.userName
        in: query
        required: false
        schema:
          type: string
      - name: user.nickName
        in: query
        required: false
        schema:
          type: string
      - name: user.email
        in: query
        required: false
        schema:
          type: string
      - name: user.phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: user.sex
        in: query
        required: false
        schema:
          type: string
      - name: user.avatar
        in: query
        required: false
        schema:
          type: string
      - name: user.password
        in: query
        required: false
        schema:
          type: string
      - name: user.status
        in: query
        required: false
        schema:
          type: string
      - name: user.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.loginIp
        in: query
        required: false
        schema:
          type: string
      - name: user.loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: user.dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.email
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.status
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: user.dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: user.roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: user.roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.remark
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /asc/verification/export:
    post:
      tags:
      - visitor-verification-controller
      operationId: export_19
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitorId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: verificationType
        in: query
        required: false
        schema:
          type: string
      - name: verificationData
        in: query
        required: false
        schema:
          type: string
      - name: verificationResult
        in: query
        required: false
        schema:
          type: string
      - name: verificationTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.name
        in: query
        required: false
        schema:
          type: string
      - name: visitor.idCard
        in: query
        required: false
        schema:
          type: string
      - name: visitor.phone
        in: query
        required: false
        schema:
          type: string
      - name: visitor.company
        in: query
        required: false
        schema:
          type: string
      - name: visitor.purpose
        in: query
        required: false
        schema:
          type: string
      - name: visitor.visitTo
        in: query
        required: false
        schema:
          type: string
      - name: visitor.visitToDept
        in: query
        required: false
        schema:
          type: string
      - name: visitor.visitToUserId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.checkInTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.checkOutTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.status
        in: query
        required: false
        schema:
          type: string
      - name: visitor.idCardVerified
        in: query
        required: false
        schema:
          type: string
      - name: visitor.verificationMethod
        in: query
        required: false
        schema:
          type: string
      - name: visitor.idPhotoRef
        in: query
        required: false
        schema:
          type: string
      - name: visitor.qrCode
        in: query
        required: false
        schema:
          type: string
      - name: visitor.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.user.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.user.userName
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.nickName
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.email
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.sex
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.avatar
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.password
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.status
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.loginIp
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.user.dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.user.dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.user.dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: visitor.user.dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.email
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.status
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: visitor.user.dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.user.dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.user.dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: visitor.user.roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: visitor.user.postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: visitor.user.roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.user.createBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.user.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.user.remark
        in: query
        required: false
        schema:
          type: string
      - name: visitor.createBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.remark
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /asc/vehicleArea/export:
    post:
      tags:
      - vehicle-area-controller
      operationId: export_20
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: vehicleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: licensePlate
        in: query
        required: false
        schema:
          type: string
      - name: areaId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: areaName
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /asc/vehicleArea/batch:
    post:
      tags:
      - vehicle-area-controller
      operationId: batchAuthorize
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: object
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicle/export:
    post:
      tags:
      - vehicle-controller
      operationId: export_21
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: licensePlate
        in: query
        required: false
        schema:
          type: string
      - name: vehicleType
        in: query
        required: false
        schema:
          type: string
      - name: vehicleColor
        in: query
        required: false
        schema:
          type: string
      - name: vehicleBrand
        in: query
        required: false
        schema:
          type: string
      - name: vehicleModel
        in: query
        required: false
        schema:
          type: string
      - name: userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: vehicleOwnerType
        in: query
        required: false
        schema:
          type: string
      - name: validStartTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: validEndTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: isBlock
        in: query
        required: false
        schema:
          type: string
      - name: isFavorite
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: authorizedAreasCount
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: user.userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.userName
        in: query
        required: false
        schema:
          type: string
      - name: user.nickName
        in: query
        required: false
        schema:
          type: string
      - name: user.email
        in: query
        required: false
        schema:
          type: string
      - name: user.phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: user.sex
        in: query
        required: false
        schema:
          type: string
      - name: user.avatar
        in: query
        required: false
        schema:
          type: string
      - name: user.password
        in: query
        required: false
        schema:
          type: string
      - name: user.status
        in: query
        required: false
        schema:
          type: string
      - name: user.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.loginIp
        in: query
        required: false
        schema:
          type: string
      - name: user.loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: user.dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.email
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.status
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: user.dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: user.roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: user.roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.remark
        in: query
        required: false
        schema:
          type: string
      - name: vehicleOwnerExcel
        in: query
        required: false
        schema:
          type: string
      - name: ownerPhoneExcel
        in: query
        required: false
        schema:
          type: string
      - name: userNameExcel
        in: query
        required: false
        schema:
          type: string
      - name: deptNameExcel
        in: query
        required: false
        schema:
          type: string
      - name: userNickName
        in: query
        required: false
        schema:
          type: string
      - name: userPhone
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /asc/record/export:
    post:
      tags:
      - license-plate-record-controller
      operationId: export_22
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deviceCode
        in: query
        required: false
        schema:
          type: string
      - name: deviceName
        in: query
        required: false
        schema:
          type: string
      - name: deviceLocation
        in: query
        required: false
        schema:
          type: string
      - name: plateNumber
        in: query
        required: false
        schema:
          type: string
      - name: plateColor
        in: query
        required: false
        schema:
          type: string
      - name: vehicleColor
        in: query
        required: false
        schema:
          type: string
      - name: vehicleType
        in: query
        required: false
        schema:
          type: string
      - name: snapTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: plateImage
        in: query
        required: false
        schema:
          type: string
      - name: vehicleImage
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      - name: userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.userName
        in: query
        required: false
        schema:
          type: string
      - name: user.nickName
        in: query
        required: false
        schema:
          type: string
      - name: user.email
        in: query
        required: false
        schema:
          type: string
      - name: user.phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: user.sex
        in: query
        required: false
        schema:
          type: string
      - name: user.avatar
        in: query
        required: false
        schema:
          type: string
      - name: user.password
        in: query
        required: false
        schema:
          type: string
      - name: user.status
        in: query
        required: false
        schema:
          type: string
      - name: user.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.loginIp
        in: query
        required: false
        schema:
          type: string
      - name: user.loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: user.dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.email
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.status
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: user.dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: user.roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: user.roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.remark
        in: query
        required: false
        schema:
          type: string
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: vehicleOwnerType
        in: query
        required: false
        schema:
          type: string
      - name: isValid
        in: query
        required: false
        schema:
          type: boolean
      - name: invalidReason
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /asc/printer/test/{id}:
    post:
      tags:
      - printer-config-controller
      operationId: getTestPrintContent
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/printer/export:
    post:
      tags:
      - printer-config-controller
      operationId: export_23
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: printerName
        in: query
        required: false
        schema:
          type: string
      - name: printerType
        in: query
        required: false
        schema:
          type: string
      - name: printerIp
        in: query
        required: false
        schema:
          type: string
      - name: printerPort
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: printerDriver
        in: query
        required: false
        schema:
          type: string
      - name: templateContent
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: isDefault
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /asc/device/export:
    post:
      tags:
      - license-plate-device-controller
      operationId: export_24
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deviceCode
        in: query
        required: false
        schema:
          type: string
      - name: deviceName
        in: query
        required: false
        schema:
          type: string
      - name: deviceType
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: deviceIp
        in: query
        required: false
        schema:
          type: string
      - name: devicePort
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: deviceLocation
        in: query
        required: false
        schema:
          type: string
      - name: areaId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: areaName
        in: query
        required: false
        schema:
          type: string
      - name: username
        in: query
        required: false
        schema:
          type: string
      - name: password
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: loginHandle
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /asc/area/export:
    post:
      tags:
      - area-controller
      operationId: export_25
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: areaCode
        in: query
        required: false
        schema:
          type: string
      - name: areaName
        in: query
        required: false
        schema:
          type: string
      - name: areaDesc
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /archives/materialclass/tree:
    post:
      tags:
      - material-class-controller
      operationId: listMaterialClassTree_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaterialClass'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /archives/material/tree:
    post:
      tags:
      - material-controller
      operationId: listMaterialClassTree_2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaterialClass'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /archives/material/importTemplate:
    post:
      tags:
      - material-controller
      operationId: importTemplate_3
      responses:
        "200":
          description: OK
  /archives/material/importData:
    post:
      tags:
      - material-controller
      operationId: importData_4
      parameters:
      - name: updateSupport
        in: query
        required: true
        schema:
          type: boolean
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /archives/material/export:
    post:
      tags:
      - material-controller
      operationId: export_26
      parameters:
      - name: materialId
        in: query
        required: false
        schema:
          type: string
      - name: materialCode
        in: query
        required: true
        schema:
          type: string
      - name: materialClassId
        in: query
        required: true
        schema:
          type: string
      - name: materialName
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.classId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: materialClass.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: materialClass.className
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: materialClass.isEnable
        in: query
        required: false
        schema:
          type: boolean
      - name: materialClass.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/MaterialClass'
      - name: materialClass.def0
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def1
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def2
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def3
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def4
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def5
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def6
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def7
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def8
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def9
        in: query
        required: false
        schema:
          type: string
      - name: materialSpec
        in: query
        required: false
        schema:
          type: string
      - name: materialType
        in: query
        required: false
        schema:
          type: string
      - name: length
        in: query
        required: false
        schema:
          type: number
          format: double
      - name: width
        in: query
        required: false
        schema:
          type: number
          format: double
      - name: height
        in: query
        required: false
        schema:
          type: number
          format: double
      - name: measurementUnit
        in: query
        required: false
        schema:
          type: string
      - name: unitWeight
        in: query
        required: false
        schema:
          type: number
          format: double
      - name: unitVolume
        in: query
        required: false
        schema:
          type: number
          format: double
      - name: creator
        in: query
        required: false
        schema:
          type: string
      - name: modifier
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: modifiedTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: isEnable
        in: query
        required: false
        schema:
          type: boolean
      - name: version
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: materialImg
        in: query
        required: false
        schema:
          type: string
      - name: def0
        in: query
        required: false
        schema:
          type: string
      - name: def1
        in: query
        required: false
        schema:
          type: string
      - name: def2
        in: query
        required: false
        schema:
          type: string
      - name: def3
        in: query
        required: false
        schema:
          type: string
      - name: def4
        in: query
        required: false
        schema:
          type: string
      - name: def5
        in: query
        required: false
        schema:
          type: string
      - name: def6
        in: query
        required: false
        schema:
          type: string
      - name: def7
        in: query
        required: false
        schema:
          type: string
      - name: def8
        in: query
        required: false
        schema:
          type: string
      - name: def9
        in: query
        required: false
        schema:
          type: string
      - name: def10
        in: query
        required: false
        schema:
          type: string
      - name: def11
        in: query
        required: false
        schema:
          type: string
      - name: def12
        in: query
        required: false
        schema:
          type: string
      - name: def13
        in: query
        required: false
        schema:
          type: string
      - name: def14
        in: query
        required: false
        schema:
          type: string
      - name: def15
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /api/log/export:
    post:
      tags:
      - api-access-log-controller
      operationId: export_27
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: apiKey
        in: query
        required: false
        schema:
          type: string
      - name: requestIp
        in: query
        required: false
        schema:
          type: string
      - name: requestUrl
        in: query
        required: false
        schema:
          type: string
      - name: requestMethod
        in: query
        required: false
        schema:
          type: string
      - name: requestParams
        in: query
        required: false
        schema:
          type: string
      - name: responseStatus
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: responseTime
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: errorMessage
        in: query
        required: false
        schema:
          type: string
      - name: accessTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
  /api/export:
    post:
      tags:
      - api-key-controller
      operationId: export_28
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: apiKey
        in: query
        required: false
        schema:
          type: string
      - name: permissions
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/ApiPermission'
      - name: remark
        in: query
        required: false
        schema:
          type: string
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: expireTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dailyLimit
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: dailyUsed
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      responses:
        "200":
          description: OK
  /visitor/getdep:
    get:
      tags:
      - old-visitor-controller
      operationId: getDep
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Dep'
  /visitor/getcontacts:
    get:
      tags:
      - old-visitor-controller
      operationId: getUser
      parameters:
      - name: depid
        in: query
        required: false
        schema:
          type: number
          default: 0
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Contacts'
  /tool/gen/{tableId}:
    get:
      tags:
      - gen-controller
      operationId: getInfo
      parameters:
      - name: tableId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /tool/gen/synchDb/{tableName}:
    get:
      tags:
      - gen-controller
      operationId: synchDb
      parameters:
      - name: tableName
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /tool/gen/preview/{tableId}:
    get:
      tags:
      - gen-controller
      operationId: preview
      parameters:
      - name: tableId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /tool/gen/list:
    get:
      tags:
      - gen-controller
      operationId: genList
      parameters:
      - name: tableId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: tableName
        in: query
        required: true
        schema:
          type: string
      - name: tableComment
        in: query
        required: true
        schema:
          type: string
      - name: subTableName
        in: query
        required: false
        schema:
          type: string
      - name: subTableFkName
        in: query
        required: false
        schema:
          type: string
      - name: className
        in: query
        required: true
        schema:
          type: string
      - name: tplCategory
        in: query
        required: false
        schema:
          type: string
      - name: packageName
        in: query
        required: true
        schema:
          type: string
      - name: moduleName
        in: query
        required: true
        schema:
          type: string
      - name: businessName
        in: query
        required: true
        schema:
          type: string
      - name: functionName
        in: query
        required: true
        schema:
          type: string
      - name: functionAuthor
        in: query
        required: true
        schema:
          type: string
      - name: genType
        in: query
        required: false
        schema:
          type: string
      - name: genPath
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.columnId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: pkColumn.tableId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: pkColumn.columnName
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.columnComment
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.columnType
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.javaType
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.javaField
        in: query
        required: true
        schema:
          type: string
      - name: pkColumn.isPk
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isIncrement
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isRequired
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isInsert
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isEdit
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isList
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isQuery
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.queryType
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.htmlType
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.dictType
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.sort
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: pkColumn.createBy
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: pkColumn.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: pkColumn.remark
        in: query
        required: false
        schema:
          type: string
      - name: columns
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/GenTableColumn'
      - name: options
        in: query
        required: false
        schema:
          type: string
      - name: treeCode
        in: query
        required: false
        schema:
          type: string
      - name: treeParentCode
        in: query
        required: false
        schema:
          type: string
      - name: treeName
        in: query
        required: false
        schema:
          type: string
      - name: parentMenuId
        in: query
        required: false
        schema:
          type: string
      - name: parentMenuName
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /tool/gen/genCode/{tableName}:
    get:
      tags:
      - gen-controller
      operationId: genCode
      parameters:
      - name: tableName
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /tool/gen/download/{tableName}:
    get:
      tags:
      - gen-controller
      operationId: download
      parameters:
      - name: tableName
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
  /tool/gen/db/list:
    get:
      tags:
      - gen-controller
      operationId: dataList
      parameters:
      - name: tableId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: tableName
        in: query
        required: true
        schema:
          type: string
      - name: tableComment
        in: query
        required: true
        schema:
          type: string
      - name: subTableName
        in: query
        required: false
        schema:
          type: string
      - name: subTableFkName
        in: query
        required: false
        schema:
          type: string
      - name: className
        in: query
        required: true
        schema:
          type: string
      - name: tplCategory
        in: query
        required: false
        schema:
          type: string
      - name: packageName
        in: query
        required: true
        schema:
          type: string
      - name: moduleName
        in: query
        required: true
        schema:
          type: string
      - name: businessName
        in: query
        required: true
        schema:
          type: string
      - name: functionName
        in: query
        required: true
        schema:
          type: string
      - name: functionAuthor
        in: query
        required: true
        schema:
          type: string
      - name: genType
        in: query
        required: false
        schema:
          type: string
      - name: genPath
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.columnId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: pkColumn.tableId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: pkColumn.columnName
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.columnComment
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.columnType
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.javaType
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.javaField
        in: query
        required: true
        schema:
          type: string
      - name: pkColumn.isPk
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isIncrement
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isRequired
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isInsert
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isEdit
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isList
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.isQuery
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.queryType
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.htmlType
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.dictType
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.sort
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: pkColumn.createBy
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: pkColumn.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: pkColumn.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: pkColumn.remark
        in: query
        required: false
        schema:
          type: string
      - name: columns
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/GenTableColumn'
      - name: options
        in: query
        required: false
        schema:
          type: string
      - name: treeCode
        in: query
        required: false
        schema:
          type: string
      - name: treeParentCode
        in: query
        required: false
        schema:
          type: string
      - name: treeName
        in: query
        required: false
        schema:
          type: string
      - name: parentMenuId
        in: query
        required: false
        schema:
          type: string
      - name: parentMenuName
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /tool/gen/column/{tableId}:
    get:
      tags:
      - gen-controller
      operationId: columnList
      parameters:
      - name: tableId
        in: query
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /tool/gen/batchGenCode:
    get:
      tags:
      - gen-controller
      operationId: batchGenCode
      parameters:
      - name: tables
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
  /test/user/{userId}:
    get:
      tags:
      - test-controller
      operationId: getUser_1
      parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RUserEntity'
    delete:
      tags:
      - test-controller
      operationId: delete
      parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RString'
  /test/user/list:
    get:
      tags:
      - test-controller
      operationId: userList
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RListUserEntity'
  /system/user/list:
    get:
      tags:
      - sys-user-controller
      operationId: list_4
      parameters:
      - name: userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: userName
        in: query
        required: false
        schema:
          type: string
      - name: nickName
        in: query
        required: false
        schema:
          type: string
      - name: email
        in: query
        required: false
        schema:
          type: string
      - name: phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: sex
        in: query
        required: false
        schema:
          type: string
      - name: avatar
        in: query
        required: false
        schema:
          type: string
      - name: password
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: loginIp
        in: query
        required: false
        schema:
          type: string
      - name: loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: dept.email
        in: query
        required: false
        schema:
          type: string
      - name: dept.status
        in: query
        required: false
        schema:
          type: string
      - name: dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /system/user/deptTree:
    get:
      tags:
      - sys-user-controller
      operationId: deptTree
      parameters:
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: ancestors
        in: query
        required: false
        schema:
          type: string
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: leader
        in: query
        required: false
        schema:
          type: string
      - name: phone
        in: query
        required: false
        schema:
          type: string
      - name: email
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: parentName
        in: query
        required: false
        schema:
          type: string
      - name: children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/authRole/{userId}:
    get:
      tags:
      - sys-user-controller
      operationId: authRole
      parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/{userId}:
    get:
      tags:
      - sys-user-controller
      operationId: getInfo_1
      parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/:
    get:
      tags:
      - sys-user-controller
      operationId: getInfo_2
      parameters: []
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role/{roleId}:
    get:
      tags:
      - sys-role-controller
      operationId: getInfo_3
      parameters:
      - name: roleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role/optionselect:
    get:
      tags:
      - sys-role-controller
      operationId: optionselect
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role/list:
    get:
      tags:
      - sys-role-controller
      operationId: list_5
      parameters:
      - name: roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: roleName
        in: query
        required: false
        schema:
          type: string
      - name: roleKey
        in: query
        required: false
        schema:
          type: string
      - name: roleSort
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: dataScope
        in: query
        required: false
        schema:
          type: string
      - name: menuCheckStrictly
        in: query
        required: false
        schema:
          type: boolean
      - name: deptCheckStrictly
        in: query
        required: false
        schema:
          type: boolean
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: flag
        in: query
        required: false
        schema:
          type: boolean
      - name: menuIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: deptIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: permissions
        in: query
        required: false
        schema:
          uniqueItems: true
          type: array
          items:
            type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /system/role/deptTree/{roleId}:
    get:
      tags:
      - sys-role-controller
      operationId: deptTree_1
      parameters:
      - name: roleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role/authUser/unallocatedList:
    get:
      tags:
      - sys-role-controller
      operationId: unallocatedList
      parameters:
      - name: userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: userName
        in: query
        required: false
        schema:
          type: string
      - name: nickName
        in: query
        required: false
        schema:
          type: string
      - name: email
        in: query
        required: false
        schema:
          type: string
      - name: phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: sex
        in: query
        required: false
        schema:
          type: string
      - name: avatar
        in: query
        required: false
        schema:
          type: string
      - name: password
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: loginIp
        in: query
        required: false
        schema:
          type: string
      - name: loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: dept.email
        in: query
        required: false
        schema:
          type: string
      - name: dept.status
        in: query
        required: false
        schema:
          type: string
      - name: dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /system/role/authUser/allocatedList:
    get:
      tags:
      - sys-role-controller
      operationId: allocatedList
      parameters:
      - name: userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: userName
        in: query
        required: false
        schema:
          type: string
      - name: nickName
        in: query
        required: false
        schema:
          type: string
      - name: email
        in: query
        required: false
        schema:
          type: string
      - name: phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: sex
        in: query
        required: false
        schema:
          type: string
      - name: avatar
        in: query
        required: false
        schema:
          type: string
      - name: password
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: loginIp
        in: query
        required: false
        schema:
          type: string
      - name: loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: dept.email
        in: query
        required: false
        schema:
          type: string
      - name: dept.status
        in: query
        required: false
        schema:
          type: string
      - name: dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /system/post/{postId}:
    get:
      tags:
      - sys-post-controller
      operationId: getInfo_4
      parameters:
      - name: postId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/post/optionselect:
    get:
      tags:
      - sys-post-controller
      operationId: optionselect_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/post/list:
    get:
      tags:
      - sys-post-controller
      operationId: list_6
      parameters:
      - name: postId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: postCode
        in: query
        required: false
        schema:
          type: string
      - name: postName
        in: query
        required: false
        schema:
          type: string
      - name: postSort
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: flag
        in: query
        required: false
        schema:
          type: boolean
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /system/notice/{noticeId}:
    get:
      tags:
      - sys-notice-controller
      operationId: getInfo_5
      parameters:
      - name: noticeId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/notice/list:
    get:
      tags:
      - sys-notice-controller
      operationId: list_7
      parameters:
      - name: noticeId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: noticeTitle
        in: query
        required: false
        schema:
          type: string
      - name: noticeType
        in: query
        required: false
        schema:
          type: string
      - name: noticeContent
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /system/menu/{menuId}:
    get:
      tags:
      - sys-menu-controller
      operationId: getInfo_6
      parameters:
      - name: menuId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    delete:
      tags:
      - sys-menu-controller
      operationId: remove_5
      parameters:
      - name: menuId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/menu/treeselect:
    get:
      tags:
      - sys-menu-controller
      operationId: treeselect
      parameters:
      - name: menuId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: menuName
        in: query
        required: false
        schema:
          type: string
      - name: parentName
        in: query
        required: false
        schema:
          type: string
      - name: parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: path
        in: query
        required: false
        schema:
          type: string
      - name: component
        in: query
        required: false
        schema:
          type: string
      - name: query
        in: query
        required: false
        schema:
          type: string
      - name: isFrame
        in: query
        required: false
        schema:
          type: string
      - name: isCache
        in: query
        required: false
        schema:
          type: string
      - name: menuType
        in: query
        required: false
        schema:
          type: string
      - name: visible
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: perms
        in: query
        required: false
        schema:
          type: string
      - name: icon
        in: query
        required: false
        schema:
          type: string
      - name: children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysMenu'
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/menu/roleMenuTreeselect/{roleId}:
    get:
      tags:
      - sys-menu-controller
      operationId: roleMenuTreeselect
      parameters:
      - name: roleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/menu/list:
    get:
      tags:
      - sys-menu-controller
      operationId: list_8
      parameters:
      - name: menuId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: menuName
        in: query
        required: false
        schema:
          type: string
      - name: parentName
        in: query
        required: false
        schema:
          type: string
      - name: parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: path
        in: query
        required: false
        schema:
          type: string
      - name: component
        in: query
        required: false
        schema:
          type: string
      - name: query
        in: query
        required: false
        schema:
          type: string
      - name: isFrame
        in: query
        required: false
        schema:
          type: string
      - name: isCache
        in: query
        required: false
        schema:
          type: string
      - name: menuType
        in: query
        required: false
        schema:
          type: string
      - name: visible
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: perms
        in: query
        required: false
        schema:
          type: string
      - name: icon
        in: query
        required: false
        schema:
          type: string
      - name: children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysMenu'
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dict/type/{dictId}:
    get:
      tags:
      - sys-dict-type-controller
      operationId: getInfo_7
      parameters:
      - name: dictId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dict/type/optionselect:
    get:
      tags:
      - sys-dict-type-controller
      operationId: optionselect_2
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dict/type/list:
    get:
      tags:
      - sys-dict-type-controller
      operationId: list_9
      parameters:
      - name: dictId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dictName
        in: query
        required: false
        schema:
          type: string
      - name: dictType
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /system/dict/data/{dictCode}:
    get:
      tags:
      - sys-dict-data-controller
      operationId: getInfo_8
      parameters:
      - name: dictCode
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dict/data/type/{dictType}:
    get:
      tags:
      - sys-dict-data-controller
      operationId: dictType
      parameters:
      - name: dictType
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dict/data/list:
    get:
      tags:
      - sys-dict-data-controller
      operationId: list_10
      parameters:
      - name: dictCode
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dictSort
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: dictLabel
        in: query
        required: false
        schema:
          type: string
      - name: dictValue
        in: query
        required: false
        schema:
          type: string
      - name: dictType
        in: query
        required: false
        schema:
          type: string
      - name: cssClass
        in: query
        required: false
        schema:
          type: string
      - name: listClass
        in: query
        required: false
        schema:
          type: string
      - name: isDefault
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /system/dept/{deptId}:
    get:
      tags:
      - sys-dept-controller
      operationId: getInfo_9
      parameters:
      - name: deptId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    delete:
      tags:
      - sys-dept-controller
      operationId: remove_8
      parameters:
      - name: deptId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dept/list:
    get:
      tags:
      - sys-dept-controller
      operationId: list_11
      parameters:
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: ancestors
        in: query
        required: false
        schema:
          type: string
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: leader
        in: query
        required: false
        schema:
          type: string
      - name: phone
        in: query
        required: false
        schema:
          type: string
      - name: email
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: parentName
        in: query
        required: false
        schema:
          type: string
      - name: children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dept/list/exclude/{deptId}:
    get:
      tags:
      - sys-dept-controller
      operationId: excludeChild
      parameters:
      - name: deptId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/config/{configId}:
    get:
      tags:
      - sys-config-controller
      operationId: getInfo_10
      parameters:
      - name: configId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/config/list:
    get:
      tags:
      - sys-config-controller
      operationId: list_12
      parameters:
      - name: configId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: configName
        in: query
        required: false
        schema:
          type: string
      - name: configKey
        in: query
        required: false
        schema:
          type: string
      - name: configValue
        in: query
        required: false
        schema:
          type: string
      - name: configType
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /system/config/configKey/{configKey}:
    get:
      tags:
      - sys-config-controller
      operationId: getConfigKey
      parameters:
      - name: configKey
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/person/query/{id}:
    get:
      tags:
      - out-sourcing-person-controller
      operationId: list_13
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/org/importTemplate:
    get:
      tags:
      - out-sourcing-org-controller
      operationId: importTemplate_4
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /outsourcing/org/TreeData:
    get:
      tags:
      - out-sourcing-org-controller
      operationId: listTreeData
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: orgName
        in: query
        required: false
        schema:
          type: string
      - name: contactPerson
        in: query
        required: false
        schema:
          type: string
      - name: contactPhone
        in: query
        required: false
        schema:
          type: string
      - name: enable
        in: query
        required: false
        schema:
          type: string
          format: byte
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OutsourcingOrg'
  /office/officeRecord/{recordId}:
    get:
      tags:
      - sys-office-record-controller
      operationId: getInfo_11
      parameters:
      - name: recordId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/officeRecord/list:
    get:
      tags:
      - sys-office-record-controller
      operationId: list_14
      parameters:
      - name: recordId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: applyId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: applyNo
        in: query
        required: false
        schema:
          type: string
      - name: detailId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: officeId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: officeName
        in: query
        required: false
        schema:
          type: string
      - name: recordType
        in: query
        required: false
        schema:
          type: string
      - name: recordNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: recordTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: operName
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /office/officeRecord/detail/{detailId}:
    get:
      tags:
      - sys-office-record-controller
      operationId: getRecordsByDetailId
      parameters:
      - name: detailId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/officeRecord/apply/{applyId}:
    get:
      tags:
      - sys-office-record-controller
      operationId: getRecordsByApplyId
      parameters:
      - name: applyId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/officeApply/{applyId}:
    get:
      tags:
      - sys-office-apply-controller
      operationId: getInfo_12
      parameters:
      - name: applyId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/officeApply/list:
    get:
      tags:
      - sys-office-apply-controller
      operationId: list_15
      parameters:
      - name: applyId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: applyNo
        in: query
        required: false
        schema:
          type: string
      - name: applyUser
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: applyUserName
        in: query
        required: false
        schema:
          type: string
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: applyTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: approveUser
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: approveUserName
        in: query
        required: false
        schema:
          type: string
      - name: approveTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: approveRemark
        in: query
        required: false
        schema:
          type: string
      - name: applyDetailList
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysOfficeApplyDetail'
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /office/office/{officeId}:
    get:
      tags:
      - sys-office-controller
      operationId: getInfo_13
      parameters:
      - name: officeId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/office/list:
    get:
      tags:
      - sys-office-controller
      operationId: list_16
      parameters:
      - name: officeId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: officeName
        in: query
        required: false
        schema:
          type: string
      - name: category
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: stock
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: unit
        in: query
        required: false
        schema:
          type: string
      - name: remark
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /office/assetRecord/{assetRecordId}:
    get:
      tags:
      - sys-asset-record-controller
      operationId: getInfo_14
      parameters:
      - name: assetRecordId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/assetRecord/list:
    get:
      tags:
      - sys-asset-record-controller
      operationId: list_17
      parameters:
      - name: recordId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: assetId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: assetName
        in: query
        required: false
        schema:
          type: string
      - name: operationType
        in: query
        required: false
        schema:
          type: string
      - name: useUserId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: useUser
        in: query
        required: false
        schema:
          type: string
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: operateBy
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: operateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /office/assetRecord/export:
    get:
      tags:
      - sys-asset-record-controller
      operationId: export_29
      parameters:
      - name: recordId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: assetId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: assetName
        in: query
        required: false
        schema:
          type: string
      - name: operationType
        in: query
        required: false
        schema:
          type: string
      - name: useUserId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: useUser
        in: query
        required: false
        schema:
          type: string
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: operateBy
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: operateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/asset/{assetId}:
    get:
      tags:
      - sys-asset-controller
      operationId: getInfo_15
      parameters:
      - name: assetId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/asset/qrcode:
    get:
      tags:
      - sys-asset-qrcode-controller
      operationId: getAssetByQrcode
      parameters:
      - name: id
        in: query
        required: true
        schema:
          minimum: 1
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/asset/list:
    get:
      tags:
      - sys-asset-controller
      operationId: list_18
      parameters:
      - name: assetId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: assetCode
        in: query
        required: true
        schema:
          maxLength: 30
          minLength: 1
          type: string
      - name: assetName
        in: query
        required: true
        schema:
          maxLength: 30
          minLength: 1
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: specification
        in: query
        required: false
        schema:
          type: string
      - name: brand
        in: query
        required: false
        schema:
          type: string
      - name: purchaseDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: purchasePrice
        in: query
        required: false
        schema:
          type: number
      - name: expectedLifeYears
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: depreciationMethod
        in: query
        required: false
        schema:
          type: string
      - name: currentValue
        in: query
        required: false
        schema:
          type: number
      - name: accumulatedDepreciation
        in: query
        required: false
        schema:
          type: number
      - name: lastDepreciationDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: useUserId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: useUser
        in: query
        required: false
        schema:
          type: string
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: location
        in: query
        required: false
        schema:
          type: string
      - name: remark
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /office/asset/depreciation/calculate/{assetId}:
    get:
      tags:
      - sys-asset-depreciation-controller
      operationId: calculateDepreciation
      parameters:
      - name: assetId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/server:
    get:
      tags:
      - server-controller
      operationId: getInfo_16
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/operlog/list:
    get:
      tags:
      - sys-operlog-controller
      operationId: list_19
      parameters:
      - name: operId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: title
        in: query
        required: false
        schema:
          type: string
      - name: businessType
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: businessTypes
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int32
      - name: method
        in: query
        required: false
        schema:
          type: string
      - name: requestMethod
        in: query
        required: false
        schema:
          type: string
      - name: operatorType
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: operName
        in: query
        required: false
        schema:
          type: string
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: operUrl
        in: query
        required: false
        schema:
          type: string
      - name: operIp
        in: query
        required: false
        schema:
          type: string
      - name: operLocation
        in: query
        required: false
        schema:
          type: string
      - name: operParam
        in: query
        required: false
        schema:
          type: string
      - name: jsonResult
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: errorMsg
        in: query
        required: false
        schema:
          type: string
      - name: operTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: costTime
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /monitor/online/list:
    get:
      tags:
      - sys-user-online-controller
      operationId: list_20
      parameters:
      - name: ipaddr
        in: query
        required: true
        schema:
          type: string
      - name: userName
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /monitor/logininfor/unlock/{userName}:
    get:
      tags:
      - sys-logininfor-controller
      operationId: unlock
      parameters:
      - name: userName
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/logininfor/list:
    get:
      tags:
      - sys-logininfor-controller
      operationId: list_21
      parameters:
      - name: infoId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: userName
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: ipaddr
        in: query
        required: false
        schema:
          type: string
      - name: loginLocation
        in: query
        required: false
        schema:
          type: string
      - name: browser
        in: query
        required: false
        schema:
          type: string
      - name: os
        in: query
        required: false
        schema:
          type: string
      - name: msg
        in: query
        required: false
        schema:
          type: string
      - name: loginTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /monitor/jobLog/{jobLogId}:
    get:
      tags:
      - sys-job-log-controller
      operationId: getInfo_17
      parameters:
      - name: jobLogId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/jobLog/list:
    get:
      tags:
      - sys-job-log-controller
      operationId: list_22
      parameters:
      - name: jobLogId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: jobName
        in: query
        required: false
        schema:
          type: string
      - name: jobGroup
        in: query
        required: false
        schema:
          type: string
      - name: invokeTarget
        in: query
        required: false
        schema:
          type: string
      - name: jobMessage
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: exceptionInfo
        in: query
        required: false
        schema:
          type: string
      - name: startTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: stopTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /monitor/job/{jobId}:
    get:
      tags:
      - sys-job-controller
      operationId: getInfo_18
      parameters:
      - name: jobId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/job/list:
    get:
      tags:
      - sys-job-controller
      operationId: list_23
      parameters:
      - name: jobId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: jobName
        in: query
        required: false
        schema:
          type: string
      - name: jobGroup
        in: query
        required: false
        schema:
          type: string
      - name: invokeTarget
        in: query
        required: false
        schema:
          type: string
      - name: cronExpression
        in: query
        required: false
        schema:
          type: string
      - name: misfirePolicy
        in: query
        required: false
        schema:
          type: string
      - name: concurrent
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /monitor/cache:
    get:
      tags:
      - cache-controller
      operationId: getInfo_19
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/cache/getValue/{cacheName}/{cacheKey}:
    get:
      tags:
      - cache-controller
      operationId: getCacheValue
      parameters:
      - name: cacheName
        in: path
        required: true
        schema:
          type: string
      - name: cacheKey
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/cache/getNames:
    get:
      tags:
      - cache-controller
      operationId: cache
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/cache/getKeys/{cacheName}:
    get:
      tags:
      - cache-controller
      operationId: getCacheKeys
      parameters:
      - name: cacheName
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/cache/clearLoginTokens:
    get:
      tags:
      - cache-controller
      operationId: clearLoginTokens
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /gy-api/material/lastupdate:
    get:
      tags:
      - api-material-controller
      operationId: lastupdate
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /gy-api/doc:
    get:
      tags:
      - api-doc-controller
      operationId: getApiDoc
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /getRouters:
    get:
      tags:
      - sys-login-controller
      operationId: getRouters
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /getInfo:
    get:
      tags:
      - sys-login-controller
      operationId: getInfo_20
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /ems/tags/{tagId}:
    get:
      tags:
      - ems-controller
      operationId: get
      parameters:
      - name: tagId
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /ems/tags/list:
    get:
      tags:
      - ems-controller
      operationId: list_24
      parameters:
      - name: tagId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: tagName
        in: query
        required: false
        schema:
          type: string
      - name: tagAddr
        in: query
        required: false
        schema:
          type: string
      - name: tagType
        in: query
        required: false
        schema:
          type: string
      - name: tagOrgid
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: tagQueryInterval
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: tagCurrentTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: tagCurrentVal
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /ems/org/{orgId}:
    get:
      tags:
      - ems-org-controller
      operationId: getInfo_21
      parameters:
      - name: orgId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    delete:
      tags:
      - ems-org-controller
      operationId: remove_18
      parameters:
      - name: orgId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /ems/org/list:
    get:
      tags:
      - ems-org-controller
      operationId: list_25
      parameters:
      - name: orgId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: orgType
        in: query
        required: false
        schema:
          type: string
      - name: orgName
        in: query
        required: false
        schema:
          type: string
      - name: parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: ancestors
        in: query
        required: false
        schema:
          type: string
      - name: children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/EMSOrg'
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /ems/org/exclude/{orgId}:
    get:
      tags:
      - ems-org-controller
      operationId: excludeChild_1
      parameters:
      - name: orgId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /common/download:
    get:
      tags:
      - common-controller
      operationId: fileDownload
      parameters:
      - name: fileName
        in: query
        required: true
        schema:
          type: string
      - name: delete
        in: query
        required: true
        schema:
          type: boolean
      responses:
        "200":
          description: OK
  /common/download/resource:
    get:
      tags:
      - common-controller
      operationId: resourceDownload
      parameters:
      - name: resource
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
  /captchaImage:
    get:
      tags:
      - captcha-controller
      operationId: getCode
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/visitor/{id}:
    get:
      tags:
      - visitor-controller
      operationId: getInfo_22
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/visitor/list:
    get:
      tags:
      - visitor-controller
      operationId: list_26
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: name
        in: query
        required: false
        schema:
          type: string
      - name: idCard
        in: query
        required: false
        schema:
          type: string
      - name: phone
        in: query
        required: false
        schema:
          type: string
      - name: company
        in: query
        required: false
        schema:
          type: string
      - name: purpose
        in: query
        required: false
        schema:
          type: string
      - name: visitTo
        in: query
        required: false
        schema:
          type: string
      - name: visitToDept
        in: query
        required: false
        schema:
          type: string
      - name: visitToUserId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: checkInTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: checkOutTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: idCardVerified
        in: query
        required: false
        schema:
          type: string
      - name: verificationMethod
        in: query
        required: false
        schema:
          type: string
      - name: idPhotoRef
        in: query
        required: false
        schema:
          type: string
      - name: qrCode
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.userName
        in: query
        required: false
        schema:
          type: string
      - name: user.nickName
        in: query
        required: false
        schema:
          type: string
      - name: user.email
        in: query
        required: false
        schema:
          type: string
      - name: user.phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: user.sex
        in: query
        required: false
        schema:
          type: string
      - name: user.avatar
        in: query
        required: false
        schema:
          type: string
      - name: user.password
        in: query
        required: false
        schema:
          type: string
      - name: user.status
        in: query
        required: false
        schema:
          type: string
      - name: user.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.loginIp
        in: query
        required: false
        schema:
          type: string
      - name: user.loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: user.dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.email
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.status
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: user.dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: user.roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: user.roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.remark
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /asc/visitor/idcard/{idCard}:
    get:
      tags:
      - visitor-controller
      operationId: getVisitorByIdCard
      parameters:
      - name: idCard
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/video/{id}:
    get:
      tags:
      - video-controller
      operationId: getInfo_23
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/video/list:
    get:
      tags:
      - video-controller
      operationId: list_27
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deviceCode
        in: query
        required: false
        schema:
          type: string
      - name: videoName
        in: query
        required: false
        schema:
          type: string
      - name: streamType
        in: query
        required: false
        schema:
          type: string
      - name: videoLocation
        in: query
        required: false
        schema:
          type: string
      - name: username
        in: query
        required: false
        schema:
          type: string
      - name: password
        in: query
        required: false
        schema:
          type: string
      - name: ip
        in: query
        required: false
        schema:
          type: string
      - name: port
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: deviceModel
        in: query
        required: false
        schema:
          type: string
      - name: rtspUrl
        in: query
        required: false
        schema:
          type: string
      - name: wsUrl
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /asc/verification/{id}:
    get:
      tags:
      - visitor-verification-controller
      operationId: getInfo_24
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/verification/visitor/{visitorId}:
    get:
      tags:
      - visitor-verification-controller
      operationId: getVerificationsByVisitorId
      parameters:
      - name: visitorId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/verification/list:
    get:
      tags:
      - visitor-verification-controller
      operationId: list_28
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitorId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: verificationType
        in: query
        required: false
        schema:
          type: string
      - name: verificationData
        in: query
        required: false
        schema:
          type: string
      - name: verificationResult
        in: query
        required: false
        schema:
          type: string
      - name: verificationTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.name
        in: query
        required: false
        schema:
          type: string
      - name: visitor.idCard
        in: query
        required: false
        schema:
          type: string
      - name: visitor.phone
        in: query
        required: false
        schema:
          type: string
      - name: visitor.company
        in: query
        required: false
        schema:
          type: string
      - name: visitor.purpose
        in: query
        required: false
        schema:
          type: string
      - name: visitor.visitTo
        in: query
        required: false
        schema:
          type: string
      - name: visitor.visitToDept
        in: query
        required: false
        schema:
          type: string
      - name: visitor.visitToUserId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.checkInTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.checkOutTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.status
        in: query
        required: false
        schema:
          type: string
      - name: visitor.idCardVerified
        in: query
        required: false
        schema:
          type: string
      - name: visitor.verificationMethod
        in: query
        required: false
        schema:
          type: string
      - name: visitor.idPhotoRef
        in: query
        required: false
        schema:
          type: string
      - name: visitor.qrCode
        in: query
        required: false
        schema:
          type: string
      - name: visitor.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.user.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.user.userName
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.nickName
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.email
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.sex
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.avatar
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.password
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.status
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.loginIp
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.user.dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.user.dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.user.dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: visitor.user.dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.email
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.status
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: visitor.user.dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.user.dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.user.dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: visitor.user.roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: visitor.user.postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: visitor.user.roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: visitor.user.createBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.user.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.user.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.user.remark
        in: query
        required: false
        schema:
          type: string
      - name: visitor.createBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: visitor.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: visitor.remark
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /asc/vehicleArea/{id}:
    get:
      tags:
      - vehicle-area-controller
      operationId: getInfo_25
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicleArea/search:
    get:
      tags:
      - vehicle-area-controller
      operationId: searchVehiclesByArea
      parameters:
      - name: areaName
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicleArea/names/{vehicleId}:
    get:
      tags:
      - vehicle-area-controller
      operationId: getAreaNamesByVehicleId
      parameters:
      - name: vehicleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicleArea/list:
    get:
      tags:
      - vehicle-area-controller
      operationId: list_29
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: vehicleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: licensePlate
        in: query
        required: false
        schema:
          type: string
      - name: areaId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: areaName
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicle/{id}:
    get:
      tags:
      - vehicle-controller
      operationId: getInfo_26
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicle/user/{userId}:
    get:
      tags:
      - vehicle-controller
      operationId: getVehiclesByUserId
      parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicle/list:
    get:
      tags:
      - vehicle-controller
      operationId: list_30
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: licensePlate
        in: query
        required: false
        schema:
          type: string
      - name: vehicleType
        in: query
        required: false
        schema:
          type: string
      - name: vehicleColor
        in: query
        required: false
        schema:
          type: string
      - name: vehicleBrand
        in: query
        required: false
        schema:
          type: string
      - name: vehicleModel
        in: query
        required: false
        schema:
          type: string
      - name: userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: vehicleOwnerType
        in: query
        required: false
        schema:
          type: string
      - name: validStartTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: validEndTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: isBlock
        in: query
        required: false
        schema:
          type: string
      - name: isFavorite
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: authorizedAreasCount
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: user.userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.userName
        in: query
        required: false
        schema:
          type: string
      - name: user.nickName
        in: query
        required: false
        schema:
          type: string
      - name: user.email
        in: query
        required: false
        schema:
          type: string
      - name: user.phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: user.sex
        in: query
        required: false
        schema:
          type: string
      - name: user.avatar
        in: query
        required: false
        schema:
          type: string
      - name: user.password
        in: query
        required: false
        schema:
          type: string
      - name: user.status
        in: query
        required: false
        schema:
          type: string
      - name: user.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.loginIp
        in: query
        required: false
        schema:
          type: string
      - name: user.loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: user.dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.email
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.status
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: user.dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: user.roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: user.roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.remark
        in: query
        required: false
        schema:
          type: string
      - name: vehicleOwnerExcel
        in: query
        required: false
        schema:
          type: string
      - name: ownerPhoneExcel
        in: query
        required: false
        schema:
          type: string
      - name: userNameExcel
        in: query
        required: false
        schema:
          type: string
      - name: deptNameExcel
        in: query
        required: false
        schema:
          type: string
      - name: userNickName
        in: query
        required: false
        schema:
          type: string
      - name: userPhone
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /asc/record/{id}:
    get:
      tags:
      - license-plate-record-controller
      operationId: getInfo_27
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/record/list:
    get:
      tags:
      - license-plate-record-controller
      operationId: list_31
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deviceCode
        in: query
        required: false
        schema:
          type: string
      - name: deviceName
        in: query
        required: false
        schema:
          type: string
      - name: deviceLocation
        in: query
        required: false
        schema:
          type: string
      - name: plateNumber
        in: query
        required: false
        schema:
          type: string
      - name: plateColor
        in: query
        required: false
        schema:
          type: string
      - name: vehicleColor
        in: query
        required: false
        schema:
          type: string
      - name: vehicleType
        in: query
        required: false
        schema:
          type: string
      - name: snapTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: plateImage
        in: query
        required: false
        schema:
          type: string
      - name: vehicleImage
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      - name: userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.userId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.userName
        in: query
        required: false
        schema:
          type: string
      - name: user.nickName
        in: query
        required: false
        schema:
          type: string
      - name: user.email
        in: query
        required: false
        schema:
          type: string
      - name: user.phonenumber
        in: query
        required: false
        schema:
          type: string
      - name: user.sex
        in: query
        required: false
        schema:
          type: string
      - name: user.avatar
        in: query
        required: false
        schema:
          type: string
      - name: user.password
        in: query
        required: false
        schema:
          type: string
      - name: user.status
        in: query
        required: false
        schema:
          type: string
      - name: user.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.loginIp
        in: query
        required: false
        schema:
          type: string
      - name: user.loginDate
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.dept.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.deptName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: user.dept.leader
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.phone
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.email
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.status
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.delFlag
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.parentName
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
      - name: user.dept.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.dept.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.dept.remark
        in: query
        required: false
        schema:
          type: string
      - name: user.roles
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
      - name: user.roleIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.postIds
        in: query
        required: false
        schema:
          type: array
          items:
            type: integer
            format: int64
      - name: user.roleId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: user.createBy
        in: query
        required: false
        schema:
          type: string
      - name: user.createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.updateBy
        in: query
        required: false
        schema:
          type: string
      - name: user.updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: user.remark
        in: query
        required: false
        schema:
          type: string
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: vehicleOwnerType
        in: query
        required: false
        schema:
          type: string
      - name: isValid
        in: query
        required: false
        schema:
          type: boolean
      - name: invalidReason
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /asc/printer/{id}:
    get:
      tags:
      - printer-config-controller
      operationId: getInfo_28
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/printer/list:
    get:
      tags:
      - printer-config-controller
      operationId: list_32
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: printerName
        in: query
        required: false
        schema:
          type: string
      - name: printerType
        in: query
        required: false
        schema:
          type: string
      - name: printerIp
        in: query
        required: false
        schema:
          type: string
      - name: printerPort
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: printerDriver
        in: query
        required: false
        schema:
          type: string
      - name: templateContent
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: isDefault
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /asc/device/{id}:
    get:
      tags:
      - license-plate-device-controller
      operationId: getInfo_29
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/device/status:
    get:
      tags:
      - license-plate-device-controller
      operationId: getDeviceStatus
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/device/list:
    get:
      tags:
      - license-plate-device-controller
      operationId: list_33
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deviceCode
        in: query
        required: false
        schema:
          type: string
      - name: deviceName
        in: query
        required: false
        schema:
          type: string
      - name: deviceType
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: deviceIp
        in: query
        required: false
        schema:
          type: string
      - name: devicePort
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: deviceLocation
        in: query
        required: false
        schema:
          type: string
      - name: areaId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: areaName
        in: query
        required: false
        schema:
          type: string
      - name: username
        in: query
        required: false
        schema:
          type: string
      - name: password
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: loginHandle
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /asc/area/{id}:
    get:
      tags:
      - area-controller
      operationId: getInfo_30
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/area/list:
    get:
      tags:
      - area-controller
      operationId: list_34
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: areaCode
        in: query
        required: false
        schema:
          type: string
      - name: areaName
        in: query
        required: false
        schema:
          type: string
      - name: areaDesc
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: delFlag
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /archives/materialclass/{id}:
    get:
      tags:
      - material-class-controller
      operationId: getDetail
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /archives/materialclass/list:
    get:
      tags:
      - material-class-controller
      operationId: list_35
      parameters:
      - name: classId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: parentId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: className
        in: query
        required: false
        schema:
          type: string
      - name: ancestors
        in: query
        required: false
        schema:
          type: string
      - name: orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: isEnable
        in: query
        required: false
        schema:
          type: boolean
      - name: children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/MaterialClass'
      - name: def0
        in: query
        required: false
        schema:
          type: string
      - name: def1
        in: query
        required: false
        schema:
          type: string
      - name: def2
        in: query
        required: false
        schema:
          type: string
      - name: def3
        in: query
        required: false
        schema:
          type: string
      - name: def4
        in: query
        required: false
        schema:
          type: string
      - name: def5
        in: query
        required: false
        schema:
          type: string
      - name: def6
        in: query
        required: false
        schema:
          type: string
      - name: def7
        in: query
        required: false
        schema:
          type: string
      - name: def8
        in: query
        required: false
        schema:
          type: string
      - name: def9
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /archives/materialclass/exclude/{classId}:
    get:
      tags:
      - material-class-controller
      operationId: excludeChild_2
      parameters:
      - name: classId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /archives/material/{id}:
    get:
      tags:
      - material-controller
      operationId: getDetail_1
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    delete:
      tags:
      - material-controller
      operationId: del_5
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /archives/material/listbyclassid/{classid}:
    get:
      tags:
      - material-controller
      operationId: listByClassId
      parameters:
      - name: classid
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /archives/material/list:
    get:
      tags:
      - material-controller
      operationId: list_36
      parameters:
      - name: materialId
        in: query
        required: false
        schema:
          type: string
      - name: materialCode
        in: query
        required: true
        schema:
          type: string
      - name: materialClassId
        in: query
        required: true
        schema:
          type: string
      - name: materialName
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.classId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: materialClass.parentId
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: materialClass.className
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.ancestors
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.orderNum
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: materialClass.isEnable
        in: query
        required: false
        schema:
          type: boolean
      - name: materialClass.children
        in: query
        required: false
        schema:
          type: array
          items:
            $ref: '#/components/schemas/MaterialClass'
      - name: materialClass.def0
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def1
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def2
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def3
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def4
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def5
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def6
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def7
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def8
        in: query
        required: false
        schema:
          type: string
      - name: materialClass.def9
        in: query
        required: false
        schema:
          type: string
      - name: materialSpec
        in: query
        required: false
        schema:
          type: string
      - name: materialType
        in: query
        required: false
        schema:
          type: string
      - name: length
        in: query
        required: false
        schema:
          type: number
          format: double
      - name: width
        in: query
        required: false
        schema:
          type: number
          format: double
      - name: height
        in: query
        required: false
        schema:
          type: number
          format: double
      - name: measurementUnit
        in: query
        required: false
        schema:
          type: string
      - name: unitWeight
        in: query
        required: false
        schema:
          type: number
          format: double
      - name: unitVolume
        in: query
        required: false
        schema:
          type: number
          format: double
      - name: creator
        in: query
        required: false
        schema:
          type: string
      - name: modifier
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: modifiedTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: isEnable
        in: query
        required: false
        schema:
          type: boolean
      - name: version
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: materialImg
        in: query
        required: false
        schema:
          type: string
      - name: def0
        in: query
        required: false
        schema:
          type: string
      - name: def1
        in: query
        required: false
        schema:
          type: string
      - name: def2
        in: query
        required: false
        schema:
          type: string
      - name: def3
        in: query
        required: false
        schema:
          type: string
      - name: def4
        in: query
        required: false
        schema:
          type: string
      - name: def5
        in: query
        required: false
        schema:
          type: string
      - name: def6
        in: query
        required: false
        schema:
          type: string
      - name: def7
        in: query
        required: false
        schema:
          type: string
      - name: def8
        in: query
        required: false
        schema:
          type: string
      - name: def9
        in: query
        required: false
        schema:
          type: string
      - name: def10
        in: query
        required: false
        schema:
          type: string
      - name: def11
        in: query
        required: false
        schema:
          type: string
      - name: def12
        in: query
        required: false
        schema:
          type: string
      - name: def13
        in: query
        required: false
        schema:
          type: string
      - name: def14
        in: query
        required: false
        schema:
          type: string
      - name: def15
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /api/{id}:
    get:
      tags:
      - api-key-controller
      operationId: getDetail_2
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
    delete:
      tags:
      - api-key-controller
      operationId: delete_1
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /api/statistics/data:
    get:
      tags:
      - api-statistics-controller
      operationId: getStatisticsData
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /api/permission/{id}:
    get:
      tags:
      - api-permission-controller
      operationId: getInfo_31
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /api/permission/list:
    get:
      tags:
      - api-permission-controller
      operationId: list_37
      parameters:
      - name: apiKeyId
        in: query
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /api/log/{id}:
    get:
      tags:
      - api-access-log-controller
      operationId: getInfo_32
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /api/log/list:
    get:
      tags:
      - api-access-log-controller
      operationId: list_38
      parameters:
      - name: id
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: apiKey
        in: query
        required: false
        schema:
          type: string
      - name: requestIp
        in: query
        required: false
        schema:
          type: string
      - name: requestUrl
        in: query
        required: false
        schema:
          type: string
      - name: requestMethod
        in: query
        required: false
        schema:
          type: string
      - name: requestParams
        in: query
        required: false
        schema:
          type: string
      - name: responseStatus
        in: query
        required: false
        schema:
          type: integer
          format: int32
      - name: responseTime
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: errorMessage
        in: query
        required: false
        schema:
          type: string
      - name: accessTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: deptId
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: deptName
        in: query
        required: false
        schema:
          type: string
      - name: status
        in: query
        required: false
        schema:
          type: string
      - name: createBy
        in: query
        required: false
        schema:
          type: string
      - name: createTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: updateBy
        in: query
        required: false
        schema:
          type: string
      - name: updateTime
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: remark
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /api/list:
    get:
      tags:
      - api-key-controller
      operationId: list_39
      parameters:
      - name: apiKey
        in: query
        required: true
        schema:
          $ref: '#/components/schemas/ApiKey'
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TableDataInfo'
  /tool/gen/{tableIds}:
    delete:
      tags:
      - gen-controller
      operationId: remove
      parameters:
      - name: tableIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/user/{userIds}:
    delete:
      tags:
      - sys-user-controller
      operationId: remove_1
      parameters:
      - name: userIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/role/{roleIds}:
    delete:
      tags:
      - sys-role-controller
      operationId: remove_2
      parameters:
      - name: roleIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/post/{postIds}:
    delete:
      tags:
      - sys-post-controller
      operationId: remove_3
      parameters:
      - name: postIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/notice/{noticeIds}:
    delete:
      tags:
      - sys-notice-controller
      operationId: remove_4
      parameters:
      - name: noticeIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dict/type/{dictIds}:
    delete:
      tags:
      - sys-dict-type-controller
      operationId: remove_6
      parameters:
      - name: dictIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dict/type/refreshCache:
    delete:
      tags:
      - sys-dict-type-controller
      operationId: refreshCache
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/dict/data/{dictCodes}:
    delete:
      tags:
      - sys-dict-data-controller
      operationId: remove_7
      parameters:
      - name: dictCodes
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/config/{configIds}:
    delete:
      tags:
      - sys-config-controller
      operationId: remove_9
      parameters:
      - name: configIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /system/config/refreshCache:
    delete:
      tags:
      - sys-config-controller
      operationId: refreshCache_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/officeApply/{applyIds}:
    delete:
      tags:
      - sys-office-apply-controller
      operationId: remove_10
      parameters:
      - name: applyIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/office/{officeIds}:
    delete:
      tags:
      - sys-office-controller
      operationId: remove_11
      parameters:
      - name: officeIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/assetRecord/{assetRecordIds}:
    delete:
      tags:
      - sys-asset-record-controller
      operationId: remove_12
      parameters:
      - name: assetRecordIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /office/asset/{assetIds}:
    delete:
      tags:
      - sys-asset-controller
      operationId: remove_13
      parameters:
      - name: assetIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/operlog/{operIds}:
    delete:
      tags:
      - sys-operlog-controller
      operationId: remove_14
      parameters:
      - name: operIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/operlog/clean:
    delete:
      tags:
      - sys-operlog-controller
      operationId: clean
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/online/{tokenId}:
    delete:
      tags:
      - sys-user-online-controller
      operationId: forceLogout
      parameters:
      - name: tokenId
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/logininfor/{infoIds}:
    delete:
      tags:
      - sys-logininfor-controller
      operationId: remove_15
      parameters:
      - name: infoIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/logininfor/clean:
    delete:
      tags:
      - sys-logininfor-controller
      operationId: clean_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/jobLog/{jobLogIds}:
    delete:
      tags:
      - sys-job-log-controller
      operationId: remove_16
      parameters:
      - name: jobLogIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/jobLog/clean:
    delete:
      tags:
      - sys-job-log-controller
      operationId: clean_2
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/job/{jobIds}:
    delete:
      tags:
      - sys-job-controller
      operationId: remove_17
      parameters:
      - name: jobIds
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/cache/clearCacheName/{cacheName}:
    delete:
      tags:
      - cache-controller
      operationId: clearCacheName
      parameters:
      - name: cacheName
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/cache/clearCacheKey/{cacheKey}:
    delete:
      tags:
      - cache-controller
      operationId: clearCacheKey
      parameters:
      - name: cacheKey
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /monitor/cache/clearCacheAll:
    delete:
      tags:
      - cache-controller
      operationId: clearCacheAll
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /ems/tags/{id}:
    delete:
      tags:
      - ems-controller
      operationId: del_3
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/visitor/{ids}:
    delete:
      tags:
      - visitor-controller
      operationId: remove_19
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/video/{ids}:
    delete:
      tags:
      - video-controller
      operationId: remove_20
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/verification/{ids}:
    delete:
      tags:
      - visitor-verification-controller
      operationId: remove_21
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicleArea/{ids}:
    delete:
      tags:
      - vehicle-area-controller
      operationId: remove_22
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicleArea/vehicle/{vehicleId}:
    delete:
      tags:
      - vehicle-area-controller
      operationId: removeByVehicleId
      parameters:
      - name: vehicleId
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicleArea/plate/{licensePlate}:
    delete:
      tags:
      - vehicle-area-controller
      operationId: removeByLicensePlate
      parameters:
      - name: licensePlate
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/vehicle/{ids}:
    delete:
      tags:
      - vehicle-controller
      operationId: remove_23
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/record/{ids}:
    delete:
      tags:
      - license-plate-record-controller
      operationId: remove_24
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/printer/{ids}:
    delete:
      tags:
      - printer-config-controller
      operationId: remove_25
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/device/{ids}:
    delete:
      tags:
      - license-plate-device-controller
      operationId: remove_26
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /asc/area/{ids}:
    delete:
      tags:
      - area-controller
      operationId: remove_27
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /archives/materialclass/{classId}:
    delete:
      tags:
      - material-class-controller
      operationId: del_4
      parameters:
      - name: classId
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /api/permission/{ids}:
    delete:
      tags:
      - api-permission-controller
      operationId: remove_28
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /api/log/{ids}:
    delete:
      tags:
      - api-access-log-controller
      operationId: remove_29
      parameters:
      - name: ids
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /api/log/clean:
    delete:
      tags:
      - api-access-log-controller
      operationId: clean_3
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AjaxResult'
  /:
    get:
      tags:
      - sys-index-controller
      operationId: index
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
    put:
      tags:
      - sys-index-controller
      operationId: index_3
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
    post:
      tags:
      - sys-index-controller
      operationId: index_2
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
    delete:
      tags:
      - sys-index-controller
      operationId: index_5
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
    options:
      tags:
      - sys-index-controller
      operationId: index_6
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
    head:
      tags:
      - sys-index-controller
      operationId: index_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
    patch:
      tags:
      - sys-index-controller
      operationId: index_4
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
components:
  schemas:
    GenTable:
      required:
      - businessName
      - className
      - functionAuthor
      - functionName
      - moduleName
      - packageName
      - tableComment
      - tableName
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        tableId:
          type: integer
          format: int64
        tableName:
          type: string
        tableComment:
          type: string
        subTableName:
          type: string
        subTableFkName:
          type: string
        className:
          type: string
        tplCategory:
          type: string
        packageName:
          type: string
        moduleName:
          type: string
        businessName:
          type: string
        functionName:
          type: string
        functionAuthor:
          type: string
        genType:
          type: string
        genPath:
          type: string
        pkColumn:
          $ref: '#/components/schemas/GenTableColumn'
        subTable:
          $ref: '#/components/schemas/GenTable'
        columns:
          type: array
          items:
            $ref: '#/components/schemas/GenTableColumn'
        options:
          type: string
        treeCode:
          type: string
        treeParentCode:
          type: string
        treeName:
          type: string
        parentMenuId:
          type: string
        parentMenuName:
          type: string
        crud:
          type: boolean
        sub:
          type: boolean
        tree:
          type: boolean
    GenTableColumn:
      required:
      - javaField
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        columnId:
          type: integer
          format: int64
        tableId:
          type: integer
          format: int64
        columnName:
          type: string
        columnComment:
          type: string
        columnType:
          type: string
        javaType:
          type: string
        javaField:
          type: string
        isPk:
          type: string
        isIncrement:
          type: string
        isRequired:
          type: string
        isInsert:
          type: string
        isEdit:
          type: string
        isList:
          type: string
        isQuery:
          type: string
        queryType:
          type: string
        htmlType:
          type: string
        dictType:
          type: string
        sort:
          type: integer
          format: int32
        required:
          type: boolean
        list:
          type: boolean
        pk:
          type: boolean
        edit:
          type: boolean
        usableColumn:
          type: boolean
        superColumn:
          type: boolean
        insert:
          type: boolean
        increment:
          type: boolean
        query:
          type: boolean
        capJavaField:
          type: string
    AjaxResult:
      type: object
      properties:
        empty:
          type: boolean
      additionalProperties:
        type: object
    UserEntity:
      type: object
      properties:
        userId:
          type: integer
          format: int32
        username:
          type: string
        password:
          type: string
        mobile:
          type: string
    RString:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          type: string
    SysDept:
      required:
      - deptName
      - orderNum
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        deptId:
          type: integer
          format: int64
        parentId:
          type: integer
          format: int64
        ancestors:
          type: string
        deptName:
          maxLength: 30
          minLength: 0
          type: string
        orderNum:
          type: integer
          format: int32
        leader:
          type: string
        phone:
          maxLength: 11
          minLength: 0
          type: string
        email:
          maxLength: 50
          minLength: 0
          type: string
        status:
          type: string
        delFlag:
          type: string
        parentName:
          type: string
        children:
          type: array
          items:
            $ref: '#/components/schemas/SysDept'
    SysRole:
      required:
      - roleKey
      - roleName
      - roleSort
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        roleId:
          type: integer
          format: int64
        roleName:
          maxLength: 30
          minLength: 0
          type: string
        roleKey:
          maxLength: 100
          minLength: 0
          type: string
        roleSort:
          type: integer
          format: int32
        dataScope:
          type: string
        menuCheckStrictly:
          type: boolean
        deptCheckStrictly:
          type: boolean
        status:
          type: string
        delFlag:
          type: string
        flag:
          type: boolean
        menuIds:
          type: array
          items:
            type: integer
            format: int64
        deptIds:
          type: array
          items:
            type: integer
            format: int64
        permissions:
          uniqueItems: true
          type: array
          items:
            type: string
        admin:
          type: boolean
    SysUser:
      required:
      - userName
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        userId:
          type: integer
          format: int64
        deptId:
          type: integer
          format: int64
        userName:
          maxLength: 30
          minLength: 0
          type: string
        nickName:
          maxLength: 30
          minLength: 0
          type: string
        email:
          maxLength: 50
          minLength: 0
          type: string
        phonenumber:
          maxLength: 11
          minLength: 0
          type: string
        sex:
          type: string
        avatar:
          type: string
        password:
          type: string
        status:
          type: string
        delFlag:
          type: string
        loginIp:
          type: string
        loginDate:
          type: string
          format: date-time
        dept:
          $ref: '#/components/schemas/SysDept'
        roles:
          type: array
          items:
            $ref: '#/components/schemas/SysRole'
        roleIds:
          type: array
          items:
            type: integer
            format: int64
        postIds:
          type: array
          items:
            type: integer
            format: int64
        roleId:
          type: integer
          format: int64
        admin:
          type: boolean
    SysUserRole:
      type: object
      properties:
        userId:
          type: integer
          format: int64
        roleId:
          type: integer
          format: int64
    SysPost:
      required:
      - postCode
      - postName
      - postSort
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        postId:
          type: integer
          format: int64
        postCode:
          maxLength: 64
          minLength: 0
          type: string
        postName:
          maxLength: 50
          minLength: 0
          type: string
        postSort:
          type: integer
          format: int32
        status:
          type: string
        flag:
          type: boolean
    SysNotice:
      required:
      - noticeTitle
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        noticeId:
          type: integer
          format: int64
        noticeTitle:
          maxLength: 50
          minLength: 0
          type: string
        noticeType:
          type: string
        noticeContent:
          type: string
        status:
          type: string
    SysMenu:
      required:
      - menuName
      - menuType
      - orderNum
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        menuId:
          type: integer
          format: int64
        menuName:
          maxLength: 50
          minLength: 0
          type: string
        parentName:
          type: string
        parentId:
          type: integer
          format: int64
        orderNum:
          type: integer
          format: int32
        path:
          maxLength: 200
          minLength: 0
          type: string
        component:
          maxLength: 200
          minLength: 0
          type: string
        query:
          type: string
        isFrame:
          type: string
        isCache:
          type: string
        menuType:
          type: string
        visible:
          type: string
        status:
          type: string
        perms:
          maxLength: 100
          minLength: 0
          type: string
        icon:
          type: string
        children:
          type: array
          items:
            $ref: '#/components/schemas/SysMenu'
    SysDictType:
      required:
      - dictName
      - dictType
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        dictId:
          type: integer
          format: int64
        dictName:
          maxLength: 100
          minLength: 0
          type: string
        dictType:
          maxLength: 100
          minLength: 0
          pattern: "^[a-z][a-z0-9_]*$"
          type: string
        status:
          type: string
    SysDictData:
      required:
      - dictLabel
      - dictType
      - dictValue
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        dictCode:
          type: integer
          format: int64
        dictSort:
          type: integer
          format: int64
        dictLabel:
          maxLength: 100
          minLength: 0
          type: string
        dictValue:
          maxLength: 100
          minLength: 0
          type: string
        dictType:
          maxLength: 100
          minLength: 0
          type: string
        cssClass:
          maxLength: 100
          minLength: 0
          type: string
        listClass:
          type: string
        isDefault:
          type: string
        status:
          type: string
        default:
          type: boolean
    SysConfig:
      required:
      - configKey
      - configName
      - configValue
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        configId:
          type: integer
          format: int64
        configName:
          maxLength: 100
          minLength: 0
          type: string
        configKey:
          maxLength: 100
          minLength: 0
          type: string
        configValue:
          maxLength: 500
          minLength: 0
          type: string
        configType:
          type: string
    SysOffice:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        officeId:
          type: integer
          format: int64
        officeName:
          type: string
        category:
          type: string
        status:
          type: string
        stock:
          type: integer
          format: int64
        unit:
          type: string
    SysOfficeApply:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        applyId:
          type: integer
          format: int64
        applyNo:
          type: string
        applyUser:
          type: integer
          format: int64
        applyUserName:
          type: string
        deptId:
          type: integer
          format: int64
        deptName:
          type: string
        applyTime:
          type: string
          format: date-time
        status:
          type: string
        approveUser:
          type: integer
          format: int64
        approveUserName:
          type: string
        approveTime:
          type: string
          format: date-time
        approveRemark:
          type: string
        applyDetailList:
          type: array
          items:
            $ref: '#/components/schemas/SysOfficeApplyDetail'
    SysOfficeApplyDetail:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        detailId:
          type: integer
          format: int64
        applyId:
          type: integer
          format: int64
        officeId:
          type: integer
          format: int64
        officeName:
          type: string
        applyNum:
          type: integer
          format: int32
        returnNum:
          type: integer
          format: int32
        actualNum:
          type: integer
          format: int32
        status:
          type: string
    SysAsset:
      required:
      - assetCode
      - assetName
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        assetId:
          type: integer
          format: int64
        assetCode:
          maxLength: 30
          minLength: 1
          type: string
        assetName:
          maxLength: 30
          minLength: 1
          type: string
        status:
          type: string
        specification:
          type: string
        brand:
          type: string
        purchaseDate:
          type: string
          format: date-time
        purchasePrice:
          type: number
        expectedLifeYears:
          type: integer
          format: int32
        depreciationMethod:
          type: string
        currentValue:
          type: number
        accumulatedDepreciation:
          type: number
        lastDepreciationDate:
          type: string
          format: date-time
        useUserId:
          type: integer
          format: int64
        useUser:
          type: string
        deptId:
          type: integer
          format: int64
        deptName:
          type: string
        location:
          type: string
    SysAssetRecord:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        recordId:
          type: integer
          format: int64
        assetId:
          type: integer
          format: int64
        assetName:
          type: string
        operationType:
          type: string
        useUserId:
          type: integer
          format: int64
        useUser:
          type: string
        deptId:
          type: integer
          format: int64
        deptName:
          type: string
        operateBy:
          type: string
        status:
          type: string
        operateTime:
          type: string
          format: date-time
    SysJob:
      required:
      - cronExpression
      - invokeTarget
      - jobName
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        jobId:
          type: integer
          format: int64
        jobName:
          maxLength: 64
          minLength: 0
          type: string
        jobGroup:
          type: string
        invokeTarget:
          maxLength: 500
          minLength: 0
          type: string
        cronExpression:
          maxLength: 255
          minLength: 0
          type: string
        misfirePolicy:
          type: string
        concurrent:
          type: string
        status:
          type: string
        nextValidTime:
          type: string
          format: date-time
    Tag:
      type: object
      properties:
        tagId:
          type: integer
          format: int32
        tagName:
          type: string
        tagAddr:
          type: string
        tagType:
          type: string
        tagOrgid:
          type: integer
          format: int32
        tagQueryInterval:
          type: integer
          format: int32
        tagCurrentTime:
          type: string
          format: date-time
        tagCurrentVal:
          type: string
    EMSOrg:
      type: object
      properties:
        orgId:
          type: integer
          format: int64
        orgType:
          type: string
        orgName:
          type: string
        parentId:
          type: integer
          format: int64
        ancestors:
          type: string
        children:
          type: array
          items:
            $ref: '#/components/schemas/EMSOrg'
    Visitor:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        id:
          type: integer
          format: int64
        name:
          type: string
        idCard:
          type: string
        phone:
          type: string
        company:
          type: string
        purpose:
          type: string
        visitTo:
          type: string
        visitToDept:
          type: string
        visitToUserId:
          type: integer
          format: int64
        checkInTime:
          type: string
          format: date-time
        checkOutTime:
          type: string
          format: date-time
        status:
          type: string
        idCardVerified:
          type: string
        verificationMethod:
          type: string
        idPhotoRef:
          type: string
        qrCode:
          type: string
        delFlag:
          type: string
        user:
          $ref: '#/components/schemas/SysUser'
    Video:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        id:
          type: integer
          format: int64
        deviceCode:
          type: string
        videoName:
          type: string
        streamType:
          type: string
        videoLocation:
          type: string
        username:
          type: string
        password:
          type: string
        ip:
          type: string
        port:
          type: integer
          format: int32
        deviceModel:
          type: string
        rtspUrl:
          type: string
        wsUrl:
          type: string
        status:
          type: string
        delFlag:
          type: string
    VisitorVerification:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        id:
          type: integer
          format: int64
        visitorId:
          type: integer
          format: int64
        verificationType:
          type: string
        verificationData:
          type: string
        verificationResult:
          type: string
        verificationTime:
          type: string
          format: date-time
        visitor:
          $ref: '#/components/schemas/Visitor'
    Vehicle:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        id:
          type: integer
          format: int64
        licensePlate:
          type: string
        vehicleType:
          type: string
        vehicleColor:
          type: string
        vehicleBrand:
          type: string
        vehicleModel:
          type: string
        userId:
          type: integer
          format: int64
        vehicleOwnerType:
          type: string
        validStartTime:
          type: string
          format: date-time
        validEndTime:
          type: string
          format: date-time
        status:
          type: string
        isBlock:
          type: string
        isFavorite:
          type: string
        delFlag:
          type: string
        areaPermissions:
          type: object
          additionalProperties:
            type: string
        authorizedAreasCount:
          type: integer
          format: int32
        user:
          $ref: '#/components/schemas/SysUser'
        vehicleOwnerExcel:
          type: string
        ownerPhoneExcel:
          type: string
        userNameExcel:
          type: string
        deptNameExcel:
          type: string
        userNickName:
          type: string
        userPhone:
          type: string
    VehicleArea:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        id:
          type: integer
          format: int64
        vehicleId:
          type: integer
          format: int64
        licensePlate:
          type: string
        areaId:
          type: integer
          format: int64
        areaName:
          type: string
        status:
          type: string
        delFlag:
          type: string
    LicensePlateRecord:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        id:
          type: integer
          format: int64
        deviceCode:
          type: string
        deviceName:
          type: string
        deviceLocation:
          type: string
        plateNumber:
          type: string
        plateColor:
          type: string
        vehicleColor:
          type: string
        vehicleType:
          type: string
        snapTime:
          type: string
          format: date-time
        plateImage:
          type: string
        vehicleImage:
          type: string
        userId:
          type: integer
          format: int64
        user:
          $ref: '#/components/schemas/SysUser'
        deptName:
          type: string
        vehicleOwnerType:
          type: string
        isValid:
          type: boolean
        invalidReason:
          type: string
    PrinterConfig:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        id:
          type: integer
          format: int64
        printerName:
          type: string
        printerType:
          type: string
        printerIp:
          type: string
        printerPort:
          type: integer
          format: int32
        printerDriver:
          type: string
        templateContent:
          type: string
        status:
          type: string
        isDefault:
          type: string
    LicensePlateDevice:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        id:
          type: integer
          format: int64
        deviceCode:
          type: string
        deviceName:
          type: string
        deviceType:
          type: integer
          format: int32
        deviceIp:
          type: string
        devicePort:
          type: integer
          format: int32
        deviceLocation:
          type: string
        areaId:
          type: integer
          format: int64
        areaName:
          type: string
        username:
          type: string
        password:
          type: string
        status:
          type: string
        delFlag:
          type: string
        loginHandle:
          type: integer
          format: int64
    Area:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        id:
          type: integer
          format: int64
        areaCode:
          type: string
        areaName:
          type: string
        areaDesc:
          type: string
        status:
          type: string
        delFlag:
          type: string
    MaterialClass:
      type: object
      properties:
        classId:
          type: integer
          format: int32
        parentId:
          type: integer
          format: int32
        className:
          type: string
        ancestors:
          type: string
        orderNum:
          type: integer
          format: int32
        isEnable:
          type: boolean
        children:
          type: array
          items:
            $ref: '#/components/schemas/MaterialClass'
        def0:
          type: string
        def1:
          type: string
        def2:
          type: string
        def3:
          type: string
        def4:
          type: string
        def5:
          type: string
        def6:
          type: string
        def7:
          type: string
        def8:
          type: string
        def9:
          type: string
    Material:
      required:
      - materialClassId
      - materialCode
      type: object
      properties:
        materialId:
          type: string
        materialCode:
          type: string
        materialClassId:
          type: string
        materialName:
          type: string
        materialClass:
          $ref: '#/components/schemas/MaterialClass'
        materialSpec:
          type: string
        materialType:
          type: string
        length:
          type: number
          format: double
        width:
          type: number
          format: double
        height:
          type: number
          format: double
        measurementUnit:
          type: string
        unitWeight:
          type: number
          format: double
        unitVolume:
          type: number
          format: double
        creator:
          type: string
        modifier:
          type: string
        createTime:
          type: string
          format: date-time
        modifiedTime:
          type: string
          format: date-time
        isEnable:
          type: boolean
        version:
          type: integer
          format: int32
        materialImg:
          type: string
        def0:
          type: string
        def1:
          type: string
        def2:
          type: string
        def3:
          type: string
        def4:
          type: string
        def5:
          type: string
        def6:
          type: string
        def7:
          type: string
        def8:
          type: string
        def9:
          type: string
        def10:
          type: string
        def11:
          type: string
        def12:
          type: string
        def13:
          type: string
        def14:
          type: string
        def15:
          type: string
    ApiKey:
      type: object
      properties:
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
        remark:
          type: string
        params:
          type: object
          additionalProperties:
            type: object
        id:
          type: integer
          format: int64
        apiKey:
          type: string
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/ApiPermission'
        deptId:
          type: integer
          format: int64
        deptName:
          type: string
        status:
          type: string
        expireTime:
          type: string
          format: date-time
        dailyLimit:
          type: integer
          format: int32
        dailyUsed:
          type: integer
          format: int32
    ApiPermission:
      type: object
      properties:
        id:
          type: integer
          format: int64
        apiKeyId:
          type: integer
          format: int64
        url:
          type: string
        method:
          type: string
        createBy:
          type: string
        createTime:
          type: string
          format: date-time
        updateBy:
          type: string
        updateTime:
          type: string
          format: date-time
    TableDataInfo:
      type: object
      properties:
        total:
          type: integer
          format: int64
        rows:
          type: array
          items:
            type: object
        code:
          type: integer
          format: int32
        msg:
          type: string
    RegisterBody:
      type: object
      properties:
        username:
          type: string
        password:
          type: string
        code:
          type: string
        uuid:
          type: string
    AssetStatsRequest:
      type: object
      properties:
        dimension:
          type: string
        metrics:
          type: string
        assetName:
          type: string
        status:
          type: string
    LoginBody:
      type: object
      properties:
        username:
          type: string
        password:
          type: string
        code:
          type: string
        uuid:
          type: string
    ApiParam:
      type: object
      properties:
        pageNum:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        orderBy:
          type: string
        reasonable:
          type: boolean
        beforeTime:
          type: string
          format: date-time
        afterTime:
          type: string
          format: date-time
    RUniAppLineChartBo:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          $ref: '#/components/schemas/UniAppLineChartBo'
    Series:
      type: object
      properties:
        name:
          type: string
        data:
          type: array
          items:
            type: string
    UniAppLineChartBo:
      type: object
      properties:
        categories:
          type: array
          items:
            type: string
        series:
          type: array
          items:
            $ref: '#/components/schemas/Series'
    Cars:
      type: object
      properties:
        carId:
          type: integer
          format: int32
        carNum:
          type: string
        carType:
          type: string
        engineNum:
          type: string
        emissionNum:
          type: string
    GroupUpdateDomain:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        comment:
          type: string
        addPersons:
          type: array
          items:
            type: integer
            format: int32
        deletePersons:
          type: array
          items:
            type: integer
            format: int32
    Dep:
      type: object
      properties:
        depId:
          type: string
        depName:
          type: string
    Contacts:
      type: object
      properties:
        contactsId:
          type: string
        contactsName:
          type: string
        depId:
          type: string
        dep:
          $ref: '#/components/schemas/Dep'
    RUserEntity:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          $ref: '#/components/schemas/UserEntity'
    RListUserEntity:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          type: array
          items:
            $ref: '#/components/schemas/UserEntity'
    OutsourcingOrg:
      type: object
      properties:
        id:
          type: integer
          format: int32
        orgName:
          type: string
        contactPerson:
          type: string
        contactPhone:
          type: string
        enable:
          type: string
          format: byte
