# Charts 子包

## 概述
这个子包包含了项目中所有与图表相关的功能，主要使用 qiun-data-charts 组件来实现数据可视化。

**重要说明：** 为了实现真正的分包效果，qiun-data-charts 组件已从全局 uni_modules 移动到此子包内，确保图表组件只在需要时才加载。

## 功能特性
- 支持折线图、柱状图等多种图表类型
- 支持实时数据更新
- 支持多种时间周期查询（小时、天、周）
- 响应式设计，适配不同屏幕尺寸

## 文件结构
```
subpackages/charts/
├── index.vue                    # 图表主页面
├── uni_modules/                 # 子包专用组件
│   └── qiun-data-charts/       # 图表组件（从全局移动而来）
└── README.md                   # 说明文档
```

## 页面路径
- 图表页面：`/subpackages/charts/index`

## 使用方式
从其他页面跳转到图表页面：
```javascript
uni.navigateTo({
  url: '/subpackages/charts/index?tagId=' + tagId + '&title=' + title
})
```

## 参数说明
- `tagId`: 标签ID，用于查询对应的图表数据
- `title`: 页面标题，会动态设置导航栏标题

## 依赖组件
- qiun-data-charts: 图表渲染组件（本地引用）
- uni-app 内置组件

## API 接口
使用 `/api/ems/ems.js` 中的 `api_query_chart` 接口获取图表数据。

## 分包优化说明
1. **组件本地化**：qiun-data-charts 组件已移动到子包内，避免全局打包
2. **按需加载**：只有访问图表功能时才下载此子包
3. **缓存清理**：修改后需要清理编译缓存重新构建
