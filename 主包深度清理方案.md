# 主包深度清理方案

## 🚨 问题分析

主包体积超过1.5MB的主要原因：

### 1. 重复页面（严重问题）
```
pages/business/     # 重复的业务页面目录
├── device/         # 与subpackages/device重复
├── ems/           # 与subpackages/ems重复
├── vehicle/       # 与subpackages/vehicle重复
├── components/    # 重复的组件
└── uni_modules/   # 重复的模块

pages/mine/        # 与subpackages/user重复
├── about/
├── avatar/
├── help/
├── info/
├── pwd/
└── setting/

pages/user/        # 空目录，完全重复
pages/ems/         # 空目录
pages/vehicle/     # 重复页面
pages/visitor/     # 与subpackages/visitor重复
pages/common/      # 与subpackages/common重复
pages/test/        # 测试页面
```

### 2. 无用文件
- 测试页面：`pages/test/test.vue`
- 空目录：`pages/auth/`, `pages/ems/`, `pages/user/`
- 重复组件：`pages/business/components/`
- 重复模块：`pages/business/uni_modules/`

## 🎯 清理目标

**主包最终结构**：
```
pages/
├── login.vue      # 登录页（必须）
├── index.vue      # 首页（必须）
├── work/
│   └── index.vue  # 工作台（TabBar必须）
└── mine/
    └── index.vue  # 我的入口（TabBar必须）
```

**预期效果**：主包减重 > 1MB

## 🔧 清理步骤

### 步骤1：删除重复的业务页面目录
```bash
# 删除整个business目录（已有subpackages对应）
Remove-Item -Recurse -Force pages\business

# 删除重复的vehicle页面
Remove-Item -Recurse -Force pages\vehicle

# 删除重复的visitor页面
Remove-Item -Recurse -Force pages\visitor

# 删除重复的common页面
Remove-Item -Recurse -Force pages\common
```

### 步骤2：删除重复的用户页面
```bash
# 保留mine/index.vue作为入口，删除其他
Remove-Item -Recurse -Force pages\mine\about
Remove-Item -Recurse -Force pages\mine\avatar
Remove-Item -Recurse -Force pages\mine\help
Remove-Item -Recurse -Force pages\mine\info
Remove-Item -Recurse -Force pages\mine\pwd
Remove-Item -Recurse -Force pages\mine\setting
Remove-Item -Recurse -Force pages\mine\features

# 删除空的user目录
Remove-Item -Recurse -Force pages\user
```

### 步骤3：删除测试和空目录
```bash
# 删除测试页面
Remove-Item -Recurse -Force pages\test

# 删除空目录
Remove-Item -Recurse -Force pages\auth
Remove-Item -Recurse -Force pages\ems
```

### 步骤4：更新pages.json配置
移除已删除页面的路由配置。

### 步骤5：更新mine/index.vue
修改为纯跳转页面，所有功能跳转到user分包。

## 📊 清理效果预估

### 删除的文件统计
| 目录 | 文件数 | 大小估算 |
|------|--------|----------|
| pages/business/ | ~20个 | ~500KB |
| pages/mine/子页面 | ~15个 | ~300KB |
| pages/vehicle/ | ~3个 | ~100KB |
| pages/visitor/ | ~2个 | ~50KB |
| pages/common/ | ~5个 | ~100KB |
| pages/test/ | ~2个 | ~50KB |
| pages/user/ | ~10个 | ~200KB |
| 空目录 | ~5个 | ~50KB |
| **总计** | **~62个** | **~1.35MB** |

### 主包最终大小
- **清理前**：>1.5MB
- **清理后**：<200KB
- **减重比例**：>85%

## ⚠️ 注意事项

### 1. 路由更新
删除页面后需要更新：
- `pages.json`中的路由配置
- 所有跳转链接的路径
- TabBar配置保持不变

### 2. 功能验证
确保所有功能通过分包正常访问：
- 用户功能 → `subpackages/user/`
- 业务功能 → `subpackages/device/`, `subpackages/ems/`等
- 通用功能 → `subpackages/common/`

### 3. 入口页面
保留的入口页面需要正确跳转：
- `pages/mine/index.vue` → 跳转到 `subpackages/user/index`

## 🚀 执行计划

### 阶段1：备份和准备
1. 备份当前pages目录
2. 确认分包功能完整
3. 准备路由更新清单

### 阶段2：批量删除
1. 删除重复目录
2. 删除无用文件
3. 清理空目录

### 阶段3：配置更新
1. 更新pages.json
2. 修改入口页面
3. 更新跳转路径

### 阶段4：验证测试
1. 编译验证
2. 功能测试
3. 性能测试

## 📋 验证清单

### 编译验证
- [ ] 项目编译无错误
- [ ] 路由配置正确
- [ ] 分包加载正常

### 功能验证
- [ ] TabBar正常工作
- [ ] 页面跳转正确
- [ ] 所有功能可访问
- [ ] 用户体验无变化

### 性能验证
- [ ] 主包大小<200KB
- [ ] 启动速度显著提升
- [ ] 分包加载正常
- [ ] 内存占用降低

通过这次深度清理，主包将减重超过1MB，实现真正的极简主包！🎯
