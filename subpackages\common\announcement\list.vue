<template>
  <view class="announcement-list-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="header-title">公司公告</view>
      <view class="header-subtitle">查看所有公司公告信息</view>
    </view>

    <!-- 公告列表 -->
    <view class="announcement-list">
      <view 
        class="announcement-item" 
        v-for="item in announcements" 
        :key="item.id"
        @click="viewDetail(item)"
      >
        <view class="announcement-content">
          <view class="announcement-title">
            {{item.title}}
            <view class="new-badge" v-if="item.isNew">新</view>
          </view>
          <view class="announcement-summary">{{item.summary}}</view>
          <view class="announcement-meta">
            <text class="announcement-date">{{item.date}}</text>
            <text class="announcement-author" v-if="item.author">{{item.author}}</text>
          </view>
        </view>
        <view class="announcement-arrow">
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="announcements.length === 0">
      <uni-icons type="info" size="48" color="#ccc"></uni-icons>
      <text class="empty-text">暂无公告信息</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" v-if="loading">
      <uni-load-more status="loading"></uni-load-more>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      announcements: [
        {
          id: 1,
          title: '关于加强办公区域安全管理的通知',
          summary: '为确保办公区域安全，请各部门严格执行安全管理制度，做好人员进出登记和物品检查工作。',
          content: '为确保办公区域安全，请各部门严格执行安全管理制度...',
          date: '2024-01-15',
          author: '行政部',
          isNew: true
        },
        {
          id: 2,
          title: '春节放假安排通知',
          summary: '根据国家法定节假日安排，现将春节放假时间通知如下，请各部门做好相关工作安排。',
          content: '根据国家法定节假日安排，现将春节放假时间通知如下...',
          date: '2024-01-10',
          author: '人事部',
          isNew: false
        },
        {
          id: 3,
          title: '新版移动办公系统上线公告',
          summary: '为提升办公效率，新版移动办公系统已正式上线，请各位同事及时更新使用。',
          content: '为提升办公效率，新版移动办公系统已正式上线...',
          date: '2024-01-08',
          author: '技术部',
          isNew: false
        },
        {
          id: 4,
          title: '员工培训计划通知',
          summary: '为提升员工专业技能，公司将组织系列培训活动，请相关人员按时参加。',
          content: '为提升员工专业技能，公司将组织系列培训活动...',
          date: '2024-01-05',
          author: '培训中心',
          isNew: false
        },
        {
          id: 5,
          title: '办公设备更新维护通知',
          summary: '为保障办公设备正常运行，将于本周末进行设备维护更新，请提前做好准备。',
          content: '为保障办公设备正常运行，将于本周末进行设备维护更新...',
          date: '2024-01-03',
          author: '设备部',
          isNew: false
        }
      ]
    }
  },
  methods: {
    viewDetail(item) {
      uni.navigateTo({
        url: `/subpackages/common/announcement/detail?id=${item.id}&title=${encodeURIComponent(item.title)}`
      })
    },
    
    loadAnnouncements() {
      this.loading = true;
      // 这里可以调用API获取公告数据
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    }
  },
  
  onLoad() {
    // 页面加载时获取公告数据
    // this.loadAnnouncements();
  },
  
  onPullDownRefresh() {
    this.loadAnnouncements();
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
  }
}
</script>

<style lang="scss">
.announcement-list-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  color: white;
  text-align: center;

  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }

  .header-subtitle {
    font-size: 24rpx;
    opacity: 0.9;
  }
}

.announcement-list {
  padding: 20rpx;

  .announcement-item {
    background: white;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    padding: 30rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
    }

    .announcement-content {
      flex: 1;

      .announcement-title {
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 12rpx;
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 10rpx;

        .new-badge {
          background: #ff4757;
          color: white;
          font-size: 20rpx;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
          line-height: 1;
        }
      }

      .announcement-summary {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
        margin-bottom: 16rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .announcement-meta {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .announcement-date {
          font-size: 22rpx;
          color: #999;
        }

        .announcement-author {
          font-size: 22rpx;
          color: #007AFF;
          background: #f0f8ff;
          padding: 4rpx 8rpx;
          border-radius: 6rpx;
        }
      }
    }

    .announcement-arrow {
      margin-left: 20rpx;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-top: 30rpx;
  }
}

.loading-state {
  padding: 40rpx;
  text-align: center;
}
</style>
