<template>
  <view class="announcement-detail-container">
    <!-- 公告标题区域 -->
    <view class="announcement-header">
      <view class="announcement-title">{{announcement.title}}</view>
      <view class="announcement-meta">
        <view class="meta-item">
          <uni-icons type="calendar" size="16" color="#999"></uni-icons>
          <text>{{announcement.date}}</text>
        </view>
        <view class="meta-item" v-if="announcement.author">
          <uni-icons type="person" size="16" color="#999"></uni-icons>
          <text>{{announcement.author}}</text>
        </view>
        <view class="new-badge" v-if="announcement.isNew">新</view>
      </view>
    </view>

    <!-- 公告内容 -->
    <view class="announcement-content">
      <view class="content-text">{{announcement.content}}</view>
      
      <!-- 附件列表 -->
      <view class="attachment-section" v-if="announcement.attachments && announcement.attachments.length > 0">
        <view class="section-title">相关附件</view>
        <view class="attachment-list">
          <view 
            class="attachment-item" 
            v-for="(attachment, index) in announcement.attachments" 
            :key="index"
            @click="downloadAttachment(attachment)"
          >
            <uni-icons type="paperclip" size="20" color="#007AFF"></uni-icons>
            <text class="attachment-name">{{attachment.name}}</text>
            <text class="attachment-size">{{attachment.size}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn secondary" @click="goBack">返回</button>
      <button class="action-btn primary" @click="shareAnnouncement">分享</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-overlay" v-if="loading">
      <uni-load-more status="loading"></uni-load-more>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      announcementId: '',
      announcement: {
        id: '',
        title: '',
        content: '',
        date: '',
        author: '',
        isNew: false,
        attachments: []
      }
    }
  },
  
  methods: {
    loadAnnouncementDetail() {
      this.loading = true;
      
      // 模拟数据，实际应该根据ID从API获取
      const mockData = {
        1: {
          id: 1,
          title: '关于加强办公区域安全管理的通知',
          content: `各部门、各位同事：

为确保办公区域安全，维护良好的工作环境，现就加强办公区域安全管理有关事项通知如下：

一、人员管理
1. 严格执行人员进出登记制度
2. 外来人员必须在前台登记并佩戴访客证
3. 员工应主动配合安保人员的检查工作

二、物品管理
1. 禁止携带危险物品进入办公区域
2. 个人贵重物品请妥善保管
3. 公司财物严禁私自带离

三、消防安全
1. 保持消防通道畅通
2. 定期检查消防设施
3. 严禁在办公区域吸烟

四、其他要求
1. 下班时请关闭电源、锁好门窗
2. 发现安全隐患及时报告
3. 配合公司安全检查工作

请各部门认真贯彻执行，确保办公区域安全稳定。

特此通知。`,
          date: '2024-01-15',
          author: '行政部',
          isNew: true,
          attachments: [
            { name: '安全管理制度.pdf', size: '2.3MB' },
            { name: '应急预案.docx', size: '1.8MB' }
          ]
        },
        2: {
          id: 2,
          title: '春节放假安排通知',
          content: `各部门、各位同事：

根据国家法定节假日安排，结合公司实际情况，现将2024年春节放假安排通知如下：

一、放假时间
2024年2月9日（除夕）至2月17日（正月初八）放假调休，共9天。
2月18日（正月初九）正常上班。

二、工作安排
1. 各部门请在放假前做好工作交接
2. 值班人员安排另行通知
3. 紧急事务请联系值班负责人

三、注意事项
1. 放假期间注意人身和财产安全
2. 合理安排出行，避开高峰期
3. 保持通讯畅通

祝大家春节快乐，阖家幸福！

特此通知。`,
          date: '2024-01-10',
          author: '人事部',
          isNew: false,
          attachments: []
        },
        3: {
          id: 3,
          title: '新版移动办公系统上线公告',
          content: `各部门、各位同事：

为提升办公效率，优化用户体验，新版移动办公系统已于今日正式上线。

一、主要更新内容
1. 界面全新设计，操作更加便捷
2. 新增车辆管理功能模块
3. 优化数据图表展示
4. 提升系统稳定性和安全性

二、使用说明
1. 请及时更新到最新版本
2. 首次使用需要重新登录
3. 如遇问题请联系技术支持

三、技术支持
联系人：技术部
电话：内线8888
邮箱：<EMAIL>

感谢大家的支持与配合！

特此公告。`,
          date: '2024-01-08',
          author: '技术部',
          isNew: false,
          attachments: [
            { name: '系统使用手册.pdf', size: '5.2MB' }
          ]
        }
      };
      
      // 模拟API延迟
      setTimeout(() => {
        const data = mockData[this.announcementId];
        if (data) {
          this.announcement = data;
        } else {
          this.announcement = {
            id: this.announcementId,
            title: '公告详情',
            content: '公告内容加载中...',
            date: new Date().toISOString().split('T')[0],
            author: '系统',
            isNew: false,
            attachments: []
          };
        }
        this.loading = false;
      }, 500);
    },
    
    downloadAttachment(attachment) {
      uni.showToast({
        title: `下载 ${attachment.name}`,
        icon: 'success'
      });
      // 这里可以实现文件下载逻辑
    },
    
    shareAnnouncement() {
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: '',
        title: this.announcement.title,
        summary: this.announcement.content.substring(0, 100) + '...',
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '分享失败',
            icon: 'error'
          });
        }
      });
    },
    
    goBack() {
      uni.navigateBack();
    }
  },
  
  onLoad(options) {
    this.announcementId = options.id || '1';
    this.loadAnnouncementDetail();
  }
}
</script>

<style lang="scss">
.announcement-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.announcement-header {
  background: white;
  padding: 40rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;

  .announcement-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    line-height: 1.4;
    margin-bottom: 20rpx;
  }

  .announcement-meta {
    display: flex;
    align-items: center;
    gap: 20rpx;
    flex-wrap: wrap;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 8rpx;
      font-size: 24rpx;
      color: #666;
    }

    .new-badge {
      background: #ff4757;
      color: white;
      font-size: 20rpx;
      padding: 4rpx 8rpx;
      border-radius: 8rpx;
      line-height: 1;
    }
  }
}

.announcement-content {
  background: white;
  margin-top: 20rpx;
  padding: 40rpx 30rpx;

  .content-text {
    font-size: 28rpx;
    line-height: 1.8;
    color: #333;
    white-space: pre-line;
  }

  .attachment-section {
    margin-top: 40rpx;
    padding-top: 30rpx;
    border-top: 1px solid #f0f0f0;

    .section-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 20rpx;
    }

    .attachment-list {
      .attachment-item {
        display: flex;
        align-items: center;
        gap: 12rpx;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 8rpx;
        margin-bottom: 12rpx;
        transition: background-color 0.3s ease;

        &:active {
          background-color: #e9ecef;
        }

        .attachment-name {
          flex: 1;
          font-size: 26rpx;
          color: #333;
        }

        .attachment-size {
          font-size: 22rpx;
          color: #999;
        }
      }
    }
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 20rpx;

  .action-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    border: none;

    &.secondary {
      background: #f8f9fa;
      color: #666;
    }

    &.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    &:active {
      opacity: 0.8;
    }
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
</style>
