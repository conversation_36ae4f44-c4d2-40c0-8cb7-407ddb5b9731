<template>
  <view class="device-detail-container">
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" icon-size="30" :content-text="loadingText"></uni-load-more>
    </view>
    
    <view v-else>
      <uni-section title="基本信息" type="line">
        <view class="info-group">
          <view class="info-item">
            <text class="label">所属区域：</text>
            <text class="value">{{deviceInfo.areaName}}</text>
          </view>
          <view class="info-item">
            <text class="label">设备地点：</text>
            <text class="value">{{deviceInfo.deviceLocation}}</text>
          </view>
          <view class="info-item">
            <text class="label">设备IP：</text>
            <text class="value">{{deviceInfo.deviceIp}}</text>
          </view>
          <view class="info-item">
            <text class="label">状态：</text>
            <text class="value status" :class="{'status-online': deviceStatus === 'CONNECTED', 'status-offline': deviceStatus !== 'CONNECTED'}">
              {{getStatusText(deviceStatus)}}
            </text>
          </view>
          <view class="info-item">
            <text class="label">最后心跳：</text>
            <text class="value">{{formatDate(deviceInfo.lastHeartbeatTime)}}</text>
          </view>
        </view>
      </uni-section>
      
      <uni-section title="操作控制" type="line">
        <view class="control-group">
          <button type="primary" @click="handleOpenGate(deviceInfo.id)" :disabled="deviceStatus !== 'CONNECTED'" class="control-btn">开闸</button>
          <button type="default" @click="refreshDeviceInfo" class="control-btn">刷新状态</button>
        </view>
      </uni-section>
    </view>
  </view>
</template>

<script>
import { getDevice, getDeviceStatus, openGate } from '@/api/system/device';
import { checkPermi } from '@/utils/permission';

export default {
  data() {
    return {
      id: null,
      deviceInfo: {},
      deviceStatus: 'UNKNOWN',
      loading: true,
      loadingText: {
        contentdown: '正在加载...',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      }
    }
  },
  onLoad(options) {
    this.id = options.id;
    this.getDeviceInfo();
  },
  methods: {
    // 获取设备详情
    getDeviceInfo() {
      this.loading = true;
      
      Promise.all([
        getDevice(this.id),
        getDeviceStatus()
      ]).then(([detailRes, statusRes]) => {
        this.deviceInfo = detailRes.data;
        
        // 从状态列表中查找当前设备的状态
        const statusItem = statusRes.data.find(item => item.id === Number(this.id));
        if (statusItem) {
          this.deviceStatus = statusItem.connectionStatus;
          
          // 如果有心跳时间且状态为UNKNOWN，则设置为CONNECTED
          if (statusItem.lastHeartbeatTime && this.deviceStatus === 'UNKNOWN') {
            this.deviceStatus = 'CONNECTED';
          }
          
          // 更新设备详情
          this.deviceInfo.lastHeartbeatTime = statusItem.lastHeartbeatTime;
        }
        
        this.loading = false;
      }).catch(err => {
        console.error('获取设备信息失败:', err);
        this.$modal.showToast('获取设备信息失败');
        this.loading = false;
      });
    },
    
    // 刷新设备信息
    refreshDeviceInfo() {
      this.$modal.showToast('正在刷新...');
      this.getDeviceInfo();
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '无心跳记录';
      
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    },
    
    // 获取状态文本
    getStatusText(status) {
      if (status === 'CONNECTED') {
        return '在线';
      } else if (status === 'DISCONNECTED') {
        return '离线';
      } else {
        return '未知';
      }
    },
    
    // 处理开闸操作
    handleOpenGate(id) {
      if (!checkPermi(['asc:device:manage'])) {
        this.$modal.showToast('您没有操作权限');
        return;
      }
      
      uni.showModal({
        title: '确认操作',
        content: '确定要进行开闸操作吗？',
        success: res => {
          if (res.confirm) {
            this.$modal.showLoading('正在开闸...');
            openGate(id).then(res => {
              this.$modal.hideLoading();
              this.$modal.showSuccess('开闸成功');
            }).catch(err => {
              this.$modal.hideLoading();
              this.$modal.showError('开闸失败: ' + (err.message || '未知错误'));
            });
          }
        }
      });
    }
  }
}
</script>

<style lang="scss">
.device-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 20px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
}

.info-group {
  padding: 0 15px;
  background-color: #fff;
}

.info-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: bold;
  width: 120px;
  color: #333;
}

.value {
  flex: 1;
  color: #666;
}

.status {
  font-weight: bold;
}

.status-online {
  color: #19BE6B;
}

.status-offline {
  color: #DD524D;
}

.control-group {
  padding: 15px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.control-btn {
  margin-bottom: 15px;
  height: 40px;
  line-height: 40px;
}

.control-btn:last-child {
  margin-bottom: 0;
}
</style>
