<template>
  <view class="container">
    <!-- 消息和新闻区域 -->
    <view class="info-section">
      <!-- 企业动态 -->
      <uni-section title="企业动态" type="line">
        <view class="empty-placeholder">
          <uni-icons type="notification" size="30" color="#999"></uni-icons>
          <text class="empty-text">暂无企业动态</text>
        </view>
      </uni-section>

      <!-- 通知公告 -->
      <uni-section title="通知公告" type="line">
        <view class="empty-placeholder">
          <uni-icons type="sound" size="30" color="#999"></uni-icons>
          <text class="empty-text">暂无通知公告</text>
        </view>
      </uni-section>
   </view>
  </view>
</template>

<script>
import { checkPermi } from '@/utils/permission';
export default {

  //简单来说，导入只是将函数引入到当前模块的作用域，
  // 而将其添加到 methods 对象则是将其暴露给 Vue 组件实例，
  // 使其成为组件的一部分，可以在组件的生命周期、模板或计算属性等地方使用。
  methods: {
    checkPermi,
  },
  computed: {

  },
}
</script>


<style lang="scss">

.container {
  padding: 32rpx;
}

.company-info {
  display: flex;
  align-items: center;
}

.info-section {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding: 10px;
}

.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  background-color: #f8f8f8;
  border-radius: 4px;

  .empty-text {
    margin-top: 10px;
    font-size: 14px;
    color: #999;
  }
}

.grid-item-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
}

.text {
  font-size: 14px;
  margin-top: 5px;
}
</style>
