<template>
  <view class="container">
    <!-- 欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-content">
        <view class="logo">
          <view class="logo-icon">高</view>
        </view>
        <view class="title">山西高义办公系统</view>
        <view class="subtitle">欢迎使用移动办公平台</view>
      </view>
    </view>

    <!-- 公司公告 -->
    <view class="announcement-section">
      <view class="section-header">
        <view class="section-title">
          <uni-icons type="sound" size="20" color="#007AFF"></uni-icons>
          <text>公司公告</text>
        </view>
        <view class="more-link" @click="viewMoreAnnouncements">
          <text>更多</text>
          <uni-icons type="arrowright" size="14" color="#999"></uni-icons>
        </view>
      </view>
      <view class="announcement-list">
        <view class="announcement-item" v-for="item in announcements" :key="item.id" @click="viewAnnouncement(item)">
          <view class="announcement-content">
            <view class="announcement-title">{{item.title}}</view>
            <view class="announcement-summary">{{item.summary}}</view>
          </view>
          <view class="announcement-meta">
            <text class="announcement-date">{{item.date}}</text>
            <view class="announcement-badge" v-if="item.isNew">新</view>
          </view>
        </view>
        <view class="no-announcement" v-if="announcements.length === 0">
          <uni-icons type="info" size="24" color="#ccc"></uni-icons>
          <text>暂无公告</text>
        </view>
      </view>
    </view>

    <!-- 快速入口（简化） -->
    <view class="quick-access">
      <view class="access-title">快速入口</view>
      <view class="access-grid">
        <view class="access-item" @click="goToWork">
          <view class="access-icon">
            <uni-icons type="home" size="28" color="#007AFF"></uni-icons>
          </view>
          <text class="access-text">工作台</text>
        </view>
        <view class="access-item" @click="goToUser">
          <view class="access-icon">
            <uni-icons type="person" size="28" color="#909399"></uni-icons>
          </view>
          <text class="access-text">个人中心</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      announcements: [
        {
          id: 1,
          title: '关于加强办公区域安全管理的通知',
          summary: '为确保办公区域安全，请各部门严格执行安全管理制度...',
          date: '2024-01-15',
          isNew: true
        },
        {
          id: 2,
          title: '春节放假安排通知',
          summary: '根据国家法定节假日安排，现将春节放假时间通知如下...',
          date: '2024-01-10',
          isNew: false
        },
        {
          id: 3,
          title: '新版移动办公系统上线公告',
          summary: '为提升办公效率，新版移动办公系统已正式上线...',
          date: '2024-01-08',
          isNew: false
        }
      ]
    }
  },
  methods: {
    goToWork() {
      uni.switchTab({
        url: '/pages/work/index'
      })
    },
    goToUser() {
      uni.switchTab({
        url: '/pages/mine/index'
      })
    },
    viewAnnouncement(item) {
      uni.navigateTo({
        url: `/subpackages/common/announcement/detail?id=${item.id}&title=${encodeURIComponent(item.title)}`
      })
    },
    viewMoreAnnouncements() {
      uni.navigateTo({
        url: '/subpackages/common/announcement/list'
      })
    }
  },
  onLoad() {
    // 这里可以添加获取公告数据的API调用
    // this.getAnnouncements()
  }
}
</script>


<style lang="scss">
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 32rpx;
}

.welcome-section {
  text-align: center;
  margin-bottom: 60rpx;

  .welcome-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);

    .logo {
      width: 120rpx;
      height: 120rpx;
      margin: 0 auto 30rpx;

      .logo-icon {
        width: 120rpx;
        height: 120rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 48rpx;
        font-weight: bold;
        color: white;
        box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
      }
    }

    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 16rpx;
    }

    .subtitle {
      font-size: 28rpx;
      color: #666;
    }
  }
}

// 公司公告样式
.announcement-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 30rpx 20rpx;
    border-bottom: 1px solid #f0f0f0;

    .section-title {
      display: flex;
      align-items: center;
      gap: 10rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;

      text {
        margin-left: 10rpx;
      }
    }

    .more-link {
      display: flex;
      align-items: center;
      gap: 5rpx;
      font-size: 24rpx;
      color: #999;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .announcement-list {
    padding: 0 30rpx 30rpx;

    .announcement-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 20rpx 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: #f8f9fa;
      }

      .announcement-content {
        flex: 1;
        margin-right: 20rpx;

        .announcement-title {
          font-size: 28rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 8rpx;
          line-height: 1.4;
        }

        .announcement-summary {
          font-size: 24rpx;
          color: #666;
          line-height: 1.3;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }

      .announcement-meta {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 10rpx;

        .announcement-date {
          font-size: 22rpx;
          color: #999;
        }

        .announcement-badge {
          background: #ff4757;
          color: white;
          font-size: 20rpx;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
          line-height: 1;
        }
      }
    }

    .no-announcement {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60rpx 0;
      color: #ccc;

      text {
        margin-top: 20rpx;
        font-size: 24rpx;
      }
    }
  }
}

// 快速入口样式
.quick-access {
  .access-title {
    font-size: 32rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 30rpx;
    text-align: center;
  }

  .access-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30rpx;

    .access-item {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 40rpx 20rpx;
      text-align: center;
      box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      .access-icon {
        margin-bottom: 20rpx;
      }

      .access-text {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
