<template>
  <view class="container">
    <!-- 欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-content">
        <view class="logo">
          <view class="logo-icon">高</view>
        </view>
        <view class="title">山西高义办公系统</view>
        <view class="subtitle">欢迎使用移动办公平台</view>
      </view>
    </view>

    <!-- 快速导航 -->
    <view class="quick-nav">
      <view class="nav-title">快速导航</view>
      <view class="nav-grid">
        <view class="nav-item" @click="goToWorkspace">
          <view class="nav-icon">
            <uni-icons type="home" size="24" color="#007AFF"></uni-icons>
          </view>
          <text class="nav-text">工作台</text>
        </view>
        <view class="nav-item" @click="goToEms">
          <view class="nav-icon">
            <uni-icons type="bars" size="24" color="#67C23A"></uni-icons>
          </view>
          <text class="nav-text">能源管理</text>
        </view>
        <view class="nav-item" @click="goToDevice">
          <view class="nav-icon">
            <uni-icons type="gear" size="24" color="#E6A23C"></uni-icons>
          </view>
          <text class="nav-text">设备管理</text>
        </view>
        <view class="nav-item" @click="goToUser">
          <view class="nav-icon">
            <uni-icons type="person" size="24" color="#909399"></uni-icons>
          </view>
          <text class="nav-text">个人中心</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    goToWorkspace() {
      uni.switchTab({
        url: '/subpackages/workspace/index'
      })
    },
    goToEms() {
      uni.navigateTo({
        url: '/subpackages/ems/index'
      })
    },
    goToDevice() {
      uni.navigateTo({
        url: '/subpackages/device/index'
      })
    },
    goToUser() {
      uni.switchTab({
        url: '/subpackages/user/index'
      })
    }
  }
}
</script>


<style lang="scss">
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 32rpx;
}

.welcome-section {
  text-align: center;
  margin-bottom: 60rpx;

  .welcome-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);

    .logo {
      width: 120rpx;
      height: 120rpx;
      margin: 0 auto 30rpx;

      .logo-icon {
        width: 120rpx;
        height: 120rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 48rpx;
        font-weight: bold;
        color: white;
        box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
      }
    }

    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 16rpx;
    }

    .subtitle {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.quick-nav {
  .nav-title {
    font-size: 32rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 30rpx;
    text-align: center;
  }

  .nav-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30rpx;

    .nav-item {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 40rpx 20rpx;
      text-align: center;
      box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      .nav-icon {
        margin-bottom: 20rpx;
      }

      .nav-text {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
