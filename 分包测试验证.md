# 分包优化测试验证清单

## 测试环境准备

### 1. 开发工具设置
- [ ] 微信开发者工具版本 ≥ 1.06.2307260
- [ ] 开启"显示分包大小"功能
  - 详情 → 本地设置 → 显示分包大小
- [ ] 清理编译缓存
  - 删除 `unpackage` 目录
  - 重新编译项目

### 2. 编译验证
- [ ] 项目编译无错误
- [ ] 所有分包正常生成
- [ ] 检查分包大小分布

## 功能测试清单

### 主包功能测试
- [ ] **登录功能**
  - 登录页面正常显示
  - 登录流程正常
  - 登录后跳转正确

- [ ] **首页功能**
  - 首页正常加载
  - 企业动态显示正常
  - 通知公告显示正常

- [ ] **工作台功能**
  - 轮播图正常显示
  - 生产管理模块可点击
  - 车辆门禁管理模块可点击
  - 权限控制正常

### EMS分包测试
- [ ] **EMS主页** (`/subpackages/ems/index`)
  - 从工作台正常跳转
  - 页面正常显示
  - 能源监控卡片可点击
  - 数据分析卡片可点击

- [ ] **EMS详情页** (`/subpackages/ems/detail`)
  - 从EMS主页正常跳转
  - 标签列表正常显示
  - 下拉刷新功能正常
  - 点击标签跳转图表正常

### 设备管理分包测试
- [ ] **设备列表** (`/subpackages/device/index`)
  - 从工作台正常跳转
  - 设备列表正常显示
  - 搜索功能正常
  - 下拉刷新功能正常
  - 设备状态显示正确

- [ ] **设备详情** (`/subpackages/device/detail`)
  - 从设备列表正常跳转
  - 设备信息显示完整
  - 开闸功能正常（有权限时）
  - 刷新状态功能正常

### 车辆管理分包测试
- [ ] **车辆列表** (`/subpackages/vehicle/index`)
  - 从工作台正常跳转
  - 车辆列表正常显示
  - 搜索功能正常
  - 分页功能正常
  - 新增/编辑/删除功能正常（有权限时）

### 用户中心分包测试
- [ ] **用户主页** (`/subpackages/user/index`)
  - TabBar跳转正常
  - 用户信息显示正确
  - 头像显示/点击正常
  - 各功能入口可点击

- [ ] **个人信息** (`/subpackages/user/info/index`)
  - 从用户主页正常跳转
  - 个人信息显示完整

- [ ] **编辑资料** (`/subpackages/user/info/edit`)
  - 从用户主页正常跳转
  - 编辑功能正常

- [ ] **修改头像** (`/subpackages/user/avatar/index`)
  - 从用户主页正常跳转
  - 头像上传功能正常

- [ ] **修改密码** (`/subpackages/user/pwd/index`)
  - 从用户主页正常跳转
  - 密码修改功能正常

- [ ] **应用设置** (`/subpackages/user/setting/index`)
  - 从用户主页正常跳转
  - 设置项显示正常

- [ ] **常见问题** (`/subpackages/user/help/index`)
  - 从用户主页正常跳转
  - 帮助内容显示正常

- [ ] **关于我们** (`/subpackages/user/about/index`)
  - 从用户主页正常跳转
  - 关于信息显示正常

### 图表分包测试
- [ ] **图表页面** (`/subpackages/charts/index`)
  - 从EMS详情页正常跳转
  - 图表正常渲染
  - 数据加载正常
  - 交互功能正常

### 通用功能分包测试
- [ ] **网页浏览** (`/subpackages/common/webview/index`)
  - 页面正常跳转
  - 网页正常加载

- [ ] **文本浏览** (`/subpackages/common/textview/index`)
  - 页面正常跳转
  - 文本正常显示

- [ ] **用户协议** (`/subpackages/common/agreement`)
  - 页面正常跳转
  - 协议内容显示正常

- [ ] **隐私政策** (`/subpackages/common/privacy`)
  - 页面正常跳转
  - 政策内容显示正常

### 访客分包测试
- [ ] **访客登记** (`/subpackages/visitor/registration`)
  - 独立分包正常加载
  - 登记功能正常
  - 表单验证正常

## 性能测试

### 分包大小验证
- [ ] **主包大小** < 2MB
- [ ] **各分包大小** < 2MB
- [ ] **总包大小**合理
- [ ] **分包数量** ≤ 20个

### 加载性能测试
- [ ] **首屏加载时间**
  - 记录优化前后对比
  - 目标：减少30-50%

- [ ] **分包加载时间**
  - 测试各分包首次加载时间
  - 验证预加载策略效果

- [ ] **内存占用**
  - 监控内存使用情况
  - 验证按需加载效果

## 兼容性测试

### 平台兼容性
- [ ] **微信小程序**
  - 开发版测试
  - 体验版测试
  - 正式版测试

- [ ] **H5版本**
  - 浏览器兼容性
  - 移动端适配

### 设备兼容性
- [ ] **Android设备**
  - 不同版本系统
  - 不同性能设备

- [ ] **iOS设备**
  - 不同版本系统
  - 不同型号设备

## 权限测试

### 角色权限验证
- [ ] **管理员角色**
  - 所有功能可访问
  - 管理功能正常

- [ ] **普通用户角色**
  - 权限控制正确
  - 无权限提示正常

### 页面权限验证
- [ ] **白名单页面**
  - 未登录可访问
  - 跳转逻辑正确

- [ ] **受保护页面**
  - 登录验证正常
  - 权限拦截正确

## 错误处理测试

### 网络异常
- [ ] **弱网环境**
  - 分包加载容错
  - 错误提示友好

- [ ] **网络中断**
  - 离线提示正常
  - 重连机制正常

### 异常场景
- [ ] **分包加载失败**
  - 错误处理正确
  - 用户提示友好

- [ ] **页面跳转失败**
  - 错误捕获正常
  - 回退机制正常

## 测试报告模板

### 测试结果记录
```
测试时间：____年__月__日
测试人员：________
测试环境：________

主包大小：____KB
分包数量：____个
总包大小：____KB

功能测试通过率：____%
性能提升情况：____%
发现问题数量：____个

主要问题：
1. ________________
2. ________________
3. ________________

优化建议：
1. ________________
2. ________________
3. ________________
```

## 验收标准

### 必须满足
- [ ] 所有功能正常运行
- [ ] 主包大小显著减少
- [ ] 无严重性能问题
- [ ] 无功能回归问题

### 期望达到
- [ ] 首屏加载时间减少30%以上
- [ ] 分包加载流畅
- [ ] 用户体验提升明显
- [ ] 代码结构更清晰
