<template>
  <view class="device-container">
    <view class="header">
      <view class="title">设备管理</view>
      <view class="subtitle">Device Management</view>
    </view>

    <view class="content">
      <view class="search-bar">
        <uni-search-bar
          v-model="searchText"
          placeholder="搜索设备名称或IP"
          @input="onSearchInput"
          @clear="onSearchClear">
        </uni-search-bar>
      </view>

      <view class="device-list">
        <view v-for="device in filteredDevices" :key="device.id" class="device-card" @click="goToDetail(device.id)">
          <view class="device-info">
            <view class="device-name">{{device.deviceLocation}}</view>
            <view class="device-ip">IP: {{device.deviceIp}}</view>
            <view class="device-area">区域: {{device.areaName}}</view>
          </view>
          <view class="device-status">
            <view class="status-dot" :class="[getStatusClass(device.status)]"></view>
            <text class="status-text">{{getStatusText(device.status)}}</text>
          </view>
        </view>
      </view>

      <view v-if="loading" class="loading">
        <uni-load-more status="loading"></uni-load-more>
      </view>

      <view v-if="!loading && filteredDevices.length === 0" class="empty">
        <text>暂无设备数据</text>
      </view>
    </view>
  </view>
</template>

<script>
import { listDevice, getDeviceStatus } from '@/api/system/device';

export default {
  name: 'DeviceIndex',
  data() {
    return {
      devices: [],
      filteredDevices: [],
      searchText: '',
      loading: true
    }
  },
  onLoad() {
    this.loadDevices();
  },
  onPullDownRefresh() {
    this.loadDevices();
    uni.stopPullDownRefresh();
  },
  methods: {
    async loadDevices() {
      try {
        this.loading = true;
        const [deviceRes, statusRes] = await Promise.all([
          listDevice({}),
          getDeviceStatus()
        ]);

        // 合并设备信息和状态信息
        this.devices = deviceRes.rows.map(device => {
          const statusItem = statusRes.data.find(status => status.id === device.id);
          return {
            ...device,
            status: statusItem ? statusItem.connectionStatus : 'UNKNOWN'
          };
        });

        this.filteredDevices = [...this.devices];
        this.loading = false;
      } catch (error) {
        console.error('加载设备列表失败:', error);
        this.$modal.showToast('加载设备列表失败');
        this.loading = false;
      }
    },

    onSearchInput(value) {
      this.filterDevices(value);
    },

    onSearchClear() {
      this.searchText = '';
      this.filteredDevices = [...this.devices];
    },

    filterDevices(searchText) {
      if (!searchText) {
        this.filteredDevices = [...this.devices];
        return;
      }

      this.filteredDevices = this.devices.filter(device =>
        device.deviceLocation.includes(searchText) ||
        device.deviceIp.includes(searchText)
      );
    },

    goToDetail(deviceId) {
      uni.navigateTo({
        url: `/subpackages/vehicle/device/detail?id=${deviceId}`
      });
    },

    getStatusClass(status) {
      switch(status) {
        case 'CONNECTED':
          return 'status-online';
        case 'DISCONNECTED':
          return 'status-offline';
        default:
          return 'status-unknown';
      }
    },

    getStatusText(status) {
      switch(status) {
        case 'CONNECTED':
          return '在线';
        case 'DISCONNECTED':
          return '离线';
        default:
          return '未知';
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.device-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #666;
  }
}

.content {
  .search-bar {
    margin-bottom: 30rpx;
  }

  .device-list {
    .device-card {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: white;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

      .device-info {
        flex: 1;

        .device-name {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }

        .device-ip, .device-area {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 4rpx;
        }
      }

      .device-status {
        display: flex;
        align-items: center;

        .status-dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          margin-right: 10rpx;

          &.status-online {
            background-color: #19BE6B;
          }

          &.status-offline {
            background-color: #DD524D;
          }

          &.status-unknown {
            background-color: #C0C4CC;
          }
        }

        .status-text {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }

  .loading, .empty {
    text-align: center;
    padding: 60rpx 0;
    color: #666;
  }
}
</style>
