# 分包迁移后的清理工作

## 需要删除的旧页面文件

### 1. 业务模块页面（已迁移到分包）
```
pages/business/
├── ems/
│   ├── index.vue  ❌ 删除（已迁移到 subpackages/ems/index.vue）
│   └── ems.vue    ❌ 删除（已迁移到 subpackages/ems/detail.vue）
├── device/
│   └── index.vue  ❌ 删除（已迁移到 subpackages/device/index.vue）
└── vehicle/
    └── index.vue  ❌ 删除（已迁移到 subpackages/vehicle/index.vue）
```

### 2. 用户相关页面（已迁移到分包）
```
pages/mine/        ❌ 整个目录删除（已迁移到 subpackages/user/）
├── index.vue
├── avatar/
├── info/
├── pwd/
├── setting/
├── help/
└── about/
```

### 3. 通用页面（已迁移到分包）
```
pages/common/      ❌ 整个目录删除（已迁移到 subpackages/common/）
├── webview/
├── textview/
├── agreement.vue
└── privacy.vue
```

### 4. 访客页面（已迁移到分包）
```
pages/visitor/     ❌ 整个目录删除（已迁移到 subpackages/visitor/）
└── registration.vue
```

### 5. 重复页面（需要清理）
```
pages/vehicle/     ❌ 删除（与 pages/business/vehicle 重复）
└── index.vue

pages/user/        ❌ 删除（与 pages/mine 重复）
├── about/
├── avatar/
├── help/
├── info/
├── pwd/
└── setting/

pages/ems/         ❌ 删除（空目录）

pages/test/        ❌ 删除（测试页面）
└── test.vue
```

## 保留的主包页面

### 核心页面（保留）
```
pages/
├── index.vue      ✅ 保留（首页）
├── login.vue      ✅ 保留（登录页）
└── work/
    └── index.vue  ✅ 保留（工作台）
```

## 清理步骤

### 1. 验证分包功能正常
在删除旧文件前，请确保：
- [ ] 所有分包页面可以正常访问
- [ ] 页面跳转路径已更新
- [ ] TabBar 配置正确
- [ ] 权限白名单已更新

### 2. 备份重要文件
```bash
# 创建备份目录
mkdir backup_pages

# 备份要删除的页面
cp -r pages/business backup_pages/
cp -r pages/mine backup_pages/
cp -r pages/common backup_pages/
cp -r pages/visitor backup_pages/
cp -r pages/vehicle backup_pages/
cp -r pages/user backup_pages/
cp -r pages/ems backup_pages/
cp -r pages/test backup_pages/
```

### 3. 删除旧页面文件
```bash
# 删除业务模块页面
rm -rf pages/business

# 删除用户相关页面
rm -rf pages/mine

# 删除通用页面
rm -rf pages/common

# 删除访客页面
rm -rf pages/visitor

# 删除重复页面
rm -rf pages/vehicle
rm -rf pages/user
rm -rf pages/ems
rm -rf pages/test
```

### 4. 清理编译缓存
```bash
# 删除编译缓存
rm -rf unpackage
```

### 5. 验证清理结果
- [ ] 检查 pages 目录只保留核心页面
- [ ] 重新编译项目
- [ ] 测试所有功能模块
- [ ] 验证分包大小分布

## 注意事项

1. **谨慎操作**：删除前请确保已完成备份
2. **逐步清理**：建议分批删除，每次删除后测试功能
3. **团队协作**：如果是团队开发，请通知所有成员
4. **版本控制**：确保在版本控制系统中提交清理记录

## 清理后的目录结构

```
├── pages/                    # 主包页面
│   ├── index.vue            # 首页
│   ├── login.vue            # 登录页
│   └── work/
│       └── index.vue        # 工作台
├── subpackages/             # 分包目录
│   ├── charts/              # 图表分包
│   ├── ems/                 # EMS分包
│   ├── device/              # 设备分包
│   ├── vehicle/             # 车辆分包
│   ├── user/                # 用户分包
│   ├── common/              # 通用分包
│   └── visitor/             # 访客分包
├── api/                     # API接口
├── components/              # 全局组件
├── static/                  # 静态资源
├── store/                   # 状态管理
├── utils/                   # 工具函数
└── uni_modules/             # UniApp模块
```

## 预期效果

清理完成后：
- 主包大小显著减少
- 目录结构更加清晰
- 消除重复页面
- 提升维护效率
